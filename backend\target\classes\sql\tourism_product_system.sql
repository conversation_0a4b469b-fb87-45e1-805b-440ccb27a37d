-- 旅游产品管理系统数据库架构
-- 创建7个相互关联的表用于管理旅游产品、订单、优惠券和激活码

USE `tourism_miniprogram`;

-- 1. 产品表 (products)
CREATE TABLE IF NOT EXISTS `products` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `scenic_id` INT NOT NULL COMMENT '关联景区ID',
    `name` VARCHAR(200) NOT NULL COMMENT '产品名称',
    `description` TEXT COMMENT '产品描述',
    `price` DECIMAL(10,2) NOT NULL COMMENT '销售价格',
    `validity_hours` INT NOT NULL DEFAULT 0 COMMENT '激活后有效期(小时)',
    `require_activation` BOOLEAN NOT NULL DEFAULT 1 COMMENT '是否需要激活码',
    `product_type` ENUM('single', 'bundle') NOT NULL DEFAULT 'single' COMMENT '产品类型',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_scenic_id` (`scenic_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_product_type` (`product_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品表';

-- 2. 产品组合包表 (product_bundles)
CREATE TABLE IF NOT EXISTS `product_bundles` (
  `bundle_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合包ID',
  `name` varchar(200) NOT NULL COMMENT '组合包名称',
  `description` text COMMENT '组合包描述',
  `cover_image` varchar(500) DEFAULT NULL COMMENT '封面图片',
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '组合包总价',
  `discount_price` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '优惠价格',
  `discount_rate` decimal(5,2) DEFAULT NULL COMMENT '折扣率',
  `validity_days` int(11) DEFAULT 365 COMMENT '有效期天数',
  `max_usage_count` int(11) DEFAULT 1 COMMENT '最大使用次数',
  `scenic_ids` json DEFAULT NULL COMMENT '包含的景区ID列表',
  `features` json DEFAULT NULL COMMENT '组合包特色功能',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`bundle_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品组合包表';

-- 3. 组合包与产品关联表 (bundle_products)
CREATE TABLE IF NOT EXISTS `bundle_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `bundle_id` int(11) NOT NULL COMMENT '组合包ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `quantity` int(11) DEFAULT 1 COMMENT '产品数量',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bundle_product` (`bundle_id`, `product_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_product_id` (`product_id`),
  CONSTRAINT `fk_bundle_products_bundle` FOREIGN KEY (`bundle_id`) REFERENCES `product_bundles` (`bundle_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_bundle_products_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组合包产品关联表';

-- 4. 订单表 (orders)
CREATE TABLE IF NOT EXISTS `orders` (
  `order_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) DEFAULT NULL COMMENT '产品ID',
  `bundle_id` int(11) DEFAULT NULL COMMENT '组合包ID',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `quantity` int(11) DEFAULT 1 COMMENT '购买数量',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总额',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` varchar(20) DEFAULT 'wechat' COMMENT '支付方式',
  `payment_status` tinyint(4) DEFAULT 0 COMMENT '支付状态：0-待支付，1-已支付，2-支付失败',
  `order_status` tinyint(4) DEFAULT 0 COMMENT '订单状态：0-待付款，1-已付款，2-已完成，3-已取消，4-退款中，5-已退款',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系人电话',
  `remark` text COMMENT '订单备注',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `cancelled_at` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`order_id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_orders_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_orders_bundle` FOREIGN KEY (`bundle_id`) REFERENCES `product_bundles` (`bundle_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 5. 数字门票表 (coupons)
CREATE TABLE IF NOT EXISTS `coupons` (
  `coupon_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '门票ID',
  `coupon_code` varchar(50) NOT NULL COMMENT '门票码',
  `order_id` int(11) NOT NULL COMMENT '关联订单ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `product_id` int(11) DEFAULT NULL COMMENT '产品ID',
  `bundle_id` int(11) DEFAULT NULL COMMENT '组合包ID',
  `product_name` varchar(200) NOT NULL COMMENT '产品名称',
  `coupon_type` tinyint(4) DEFAULT 1 COMMENT '门票类型：1-单次，2-多次',
  `total_usage_count` int(11) DEFAULT 1 COMMENT '总使用次数',
  `used_count` int(11) DEFAULT 0 COMMENT '已使用次数',
  `scenic_id` varchar(100) DEFAULT NULL COMMENT '适用景区ID',
  `scenic_name` varchar(200) DEFAULT NULL COMMENT '景区名称',
  `valid_from` timestamp NULL DEFAULT NULL COMMENT '有效期开始时间',
  `valid_until` timestamp NULL DEFAULT NULL COMMENT '有效期结束时间',
  `qr_code_url` varchar(500) DEFAULT NULL COMMENT '二维码图片URL',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：1-有效，2-已使用，3-已过期，4-已作废',
  `activated_at` timestamp NULL DEFAULT NULL COMMENT '激活时间',
  `first_used_at` timestamp NULL DEFAULT NULL COMMENT '首次使用时间',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`coupon_id`),
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_scenic_id` (`scenic_id`),
  KEY `idx_status` (`status`),
  KEY `idx_valid_until` (`valid_until`),
  CONSTRAINT `fk_coupons_order` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_coupons_product` FOREIGN KEY (`product_id`) REFERENCES `products` (`product_id`) ON DELETE SET NULL,
  CONSTRAINT `fk_coupons_bundle` FOREIGN KEY (`bundle_id`) REFERENCES `product_bundles` (`bundle_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数字门票表';
