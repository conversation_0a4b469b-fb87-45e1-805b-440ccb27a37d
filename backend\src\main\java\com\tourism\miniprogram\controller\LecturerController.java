package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Lecturer;
import com.tourism.miniprogram.service.LecturerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 讲师控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/lecturers")
@Api(tags = "讲师管理")
public class LecturerController {

    @Autowired
    private LecturerService lecturerService;

    /**
     * 获取讲师列表
     */
    @GetMapping
    @ApiOperation(value = "获取讲师列表", notes = "获取所有启用的讲师列表")
    public Result<List<Lecturer>> getLecturers() {
        try {
            List<Lecturer> lecturers = lecturerService.getEnabledLecturers();
            return Result.success(lecturers);
        } catch (Exception e) {
            log.error("获取讲师列表失败", e);
            return Result.error("获取讲师列表失败");
        }
    }

    /**
     * 分页获取讲师列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲师列表", notes = "分页获取讲师列表，支持按姓名、头衔筛选")
    public Result<IPage<Lecturer>> getLecturerPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "讲师姓名") @RequestParam(required = false) String name,
            @ApiParam(value = "头衔") @RequestParam(required = false) String title,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<Lecturer> page = new Page<>(current, size);
            QueryWrapper<Lecturer> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (StringUtils.hasText(title)) {
                queryWrapper.like("title", title);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByAsc("sort").orderByDesc("lecturer_id");

            IPage<Lecturer> lecturerPage = lecturerService.page(page, queryWrapper);
            return Result.success(lecturerPage);
        } catch (Exception e) {
            log.error("分页获取讲师列表失败", e);
            return Result.error("获取讲师列表失败");
        }
    }

    /**
     * 获取讲师详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲师详情", notes = "根据ID获取讲师详细信息")
    public Result<Lecturer> getLecturerById(@ApiParam(value = "讲师ID", required = true) @PathVariable Integer id) {
        try {
            Lecturer lecturer = lecturerService.getById(id);
            if (lecturer == null) {
                return Result.error(404, "讲师不存在");
            }
            return Result.success(lecturer);
        } catch (Exception e) {
            log.error("获取讲师详情失败，id: {}", id, e);
            return Result.error("获取讲师详情失败");
        }
    }

    /**
     * 创建讲师
     */
    @PostMapping
    @ApiOperation(value = "创建讲师", notes = "创建新的讲师")
    public Result<String> createLecturer(@RequestBody @Valid Lecturer lecturer) {
        try {
            boolean success = lecturerService.save(lecturer);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲师失败", e);
            return Result.error("创建讲师失败");
        }
    }

    /**
     * 更新讲师
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲师", notes = "更新讲师信息")
    public Result<String> updateLecturer(
            @ApiParam(value = "讲师ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Lecturer lecturer) {
        try {
            Lecturer existLecturer = lecturerService.getById(id);
            if (existLecturer == null) {
                return Result.error(404, "讲师不存在");
            }

            lecturer.setLecturerId(id);
            boolean success = lecturerService.updateById(lecturer);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲师失败，id: {}", id, e);
            return Result.error("更新讲师失败");
        }
    }

    /**
     * 删除讲师
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲师", notes = "删除讲师")
    public Result<String> deleteLecturer(@ApiParam(value = "讲师ID", required = true) @PathVariable Integer id) {
        try {
            Lecturer lecturer = lecturerService.getById(id);
            if (lecturer == null) {
                return Result.error(404, "讲师不存在");
            }

            boolean success = lecturerService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲师失败，id: {}", id, e);
            return Result.error("删除讲师失败");
        }
    }
}
