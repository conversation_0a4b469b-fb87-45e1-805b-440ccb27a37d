package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.Lecturer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 讲师Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface LecturerMapper extends BaseMapper<Lecturer> {

    /**
     * 获取启用的讲师列表
     *
     * @return 讲师列表
     */
    @Select("SELECT * FROM lecturer WHERE status = 1 ORDER BY sort ASC, lecturer_id ASC")
    List<Lecturer> selectEnabledLecturers();
}
