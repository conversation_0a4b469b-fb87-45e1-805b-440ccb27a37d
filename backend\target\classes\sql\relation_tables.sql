-- 关系管理表结构初始化脚本
-- 创建讲解区域、音频表和多对多关系表

USE tourism_miniprogram;

-- 创建讲解区域表
CREATE TABLE IF NOT EXISTS `explanation_area` (
  `area_id` int NOT NULL AUTO_INCREMENT COMMENT '讲解区域ID',
  `area_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域名称',
  `area_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '区域描述',
  `area_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域图片URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`area_id`) USING BTREE,
  INDEX `idx_area_status` (`status` ASC) USING BTREE,
  INDEX `idx_area_sort` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解区域表' ROW_FORMAT = Dynamic;

-- 创建讲解音频表
CREATE TABLE IF NOT EXISTS `explanation_audio` (
  `audio_id` int NOT NULL AUTO_INCREMENT COMMENT '音频ID',
  `audio_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '音频名称',
  `audio_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '音频描述',
  `audio_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '音频文件URL',
  `audio_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '音频关联图片URL',
  `duration` int NULL DEFAULT 0 COMMENT '音频时长(秒)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`audio_id`) USING BTREE,
  INDEX `idx_audio_status` (`status` ASC) USING BTREE,
  INDEX `idx_audio_sort` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解音频表' ROW_FORMAT = Dynamic;

-- 创建产品区域关系表
CREATE TABLE IF NOT EXISTS `product_area_relation` (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `product_id` int NOT NULL COMMENT '产品ID',
  `area_id` int NOT NULL COMMENT '区域ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '区域在产品中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_product_area` (`product_id` ASC, `area_id` ASC) USING BTREE,
  INDEX `idx_product_id` (`product_id` ASC) USING BTREE,
  INDEX `idx_area_id` (`area_id` ASC) USING BTREE,
  CONSTRAINT `product_area_relation_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `product_table` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `product_area_relation_ibfk_2` FOREIGN KEY (`area_id`) REFERENCES `explanation_area` (`area_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品与区域多对多关联表' ROW_FORMAT = Dynamic;

-- 创建区域讲解点关系表
CREATE TABLE IF NOT EXISTS `area_point_relation` (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `area_id` int NOT NULL COMMENT '区域ID',
  `point_id` int NOT NULL COMMENT '讲解点ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '讲解点在区域中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_area_point` (`area_id` ASC, `point_id` ASC) USING BTREE,
  INDEX `idx_area_id` (`area_id` ASC) USING BTREE,
  INDEX `idx_point_id` (`point_id` ASC) USING BTREE,
  CONSTRAINT `area_point_relation_ibfk_1` FOREIGN KEY (`area_id`) REFERENCES `explanation_area` (`area_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `area_point_relation_ibfk_2` FOREIGN KEY (`point_id`) REFERENCES `point_table` (`point_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '区域与讲解点多对多关联表' ROW_FORMAT = Dynamic;

-- 创建讲解点音频关系表
CREATE TABLE IF NOT EXISTS `point_audio_relation` (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `point_id` int NOT NULL COMMENT '讲解点ID',
  `audio_id` int NOT NULL COMMENT '音频ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '音频在点中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_point_audio` (`point_id` ASC, `audio_id` ASC) USING BTREE,
  INDEX `idx_point_id` (`point_id` ASC) USING BTREE,
  INDEX `idx_audio_id` (`audio_id` ASC) USING BTREE,
  CONSTRAINT `point_audio_relation_ibfk_1` FOREIGN KEY (`point_id`) REFERENCES `point_table` (`point_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `point_audio_relation_ibfk_2` FOREIGN KEY (`audio_id`) REFERENCES `explanation_audio` (`audio_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解点与音频多对多关联表' ROW_FORMAT = Dynamic;

-- 插入示例数据

-- 插入示例区域数据
INSERT IGNORE INTO `explanation_area` (`area_name`, `area_description`, `sort_order`, `status`) VALUES
('入口区域', '景区主要入口和接待区域', 1, 1),
('核心景观区', '景区最重要的景观和文物区域', 2, 1),
('休闲娱乐区', '游客休息和娱乐设施区域', 3, 1),
('文化展示区', '历史文化展览和介绍区域', 4, 1),
('自然风光区', '自然景观和生态保护区域', 5, 1);

-- 插入示例音频数据
INSERT IGNORE INTO `explanation_audio` (`audio_name`, `audio_description`, `audio_url`, `duration`, `sort_order`, `status`) VALUES
('欢迎词', '景区欢迎介绍音频', '/audio/welcome.mp3', 120, 1, 1),
('历史背景', '景区历史背景介绍', '/audio/history.mp3', 300, 2, 1),
('建筑特色', '主要建筑特色讲解', '/audio/architecture.mp3', 240, 3, 1),
('文化内涵', '文化内涵深度解读', '/audio/culture.mp3', 360, 4, 1),
('游览提示', '游览注意事项和建议', '/audio/tips.mp3', 180, 5, 1);

-- 提示信息
SELECT '关系管理表结构初始化完成！' AS message;
SELECT '已创建以下表：' AS info;
SELECT 'explanation_area - 讲解区域表' AS table_info;
SELECT 'explanation_audio - 讲解音频表' AS table_info;
SELECT 'product_area_relation - 产品区域关系表' AS table_info;
SELECT 'area_point_relation - 区域讲解点关系表' AS table_info;
SELECT 'point_audio_relation - 讲解点音频关系表' AS table_info;
