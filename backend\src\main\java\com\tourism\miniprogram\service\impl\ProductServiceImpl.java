package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Product;
import com.tourism.miniprogram.mapper.ProductMapper;
import com.tourism.miniprogram.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 产品服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    @Override
    public List<Product> getEnabledProducts() {
        try {
            return baseMapper.selectEnabledProducts();
        } catch (Exception e) {
            log.error("获取启用产品列表失败", e);
            throw new RuntimeException("获取启用产品列表失败");
        }
    }

    @Override
    public List<Product> getProductsByScenicId(String scenicId) {
        try {
            return baseMapper.selectProductsByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取产品列表失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("根据景区ID获取产品列表失败");
        }
    }

    @Override
    public List<Product> getProductsByType(String productType) {
        try {
            return baseMapper.selectProductsByType(productType);
        } catch (Exception e) {
            log.error("根据产品类型获取产品列表失败，productType: {}", productType, e);
            throw new RuntimeException("根据产品类型获取产品列表失败");
        }
    }
}
