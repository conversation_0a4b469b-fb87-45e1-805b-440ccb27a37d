package com.tourism.miniprogram;

import com.tourism.miniprogram.entity.ProductAreaRelation;
import com.tourism.miniprogram.entity.AreaPointRelation;
import com.tourism.miniprogram.entity.PointAudioRelation;
import com.tourism.miniprogram.service.ProductAreaRelationService;
import com.tourism.miniprogram.service.AreaPointRelationService;
import com.tourism.miniprogram.service.PointAudioRelationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 关系管理功能测试
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class RelationManagementTest {

    @Autowired
    private ProductAreaRelationService productAreaRelationService;

    @Autowired
    private AreaPointRelationService areaPointRelationService;

    @Autowired
    private PointAudioRelationService pointAudioRelationService;

    @Test
    public void testProductAreaRelation() {
        // 测试添加产品区域关系
        boolean result = productAreaRelationService.addRelation(1, 1, 1);
        assertTrue(result, "添加产品区域关系应该成功");

        // 测试获取关系列表
        List<ProductAreaRelation> relations = productAreaRelationService.getByProductId(1);
        assertNotNull(relations, "关系列表不应该为空");

        // 测试删除关系
        boolean deleteResult = productAreaRelationService.removeRelation(1, 1);
        assertTrue(deleteResult, "删除产品区域关系应该成功");
    }

    @Test
    public void testAreaPointRelation() {
        // 测试添加区域讲解点关系
        boolean result = areaPointRelationService.addRelation(1, 1, 1);
        assertTrue(result, "添加区域讲解点关系应该成功");

        // 测试获取关系列表
        List<AreaPointRelation> relations = areaPointRelationService.getByAreaId(1);
        assertNotNull(relations, "关系列表不应该为空");

        // 测试删除关系
        boolean deleteResult = areaPointRelationService.removeRelation(1, 1);
        assertTrue(deleteResult, "删除区域讲解点关系应该成功");
    }

    @Test
    public void testPointAudioRelation() {
        // 测试添加讲解点音频关系
        boolean result = pointAudioRelationService.addRelation(1, 1, 1);
        assertTrue(result, "添加讲解点音频关系应该成功");

        // 测试获取关系列表
        List<PointAudioRelation> relations = pointAudioRelationService.getByPointId(1);
        assertNotNull(relations, "关系列表不应该为空");

        // 测试删除关系
        boolean deleteResult = pointAudioRelationService.removeRelation(1, 1);
        assertTrue(deleteResult, "删除讲解点音频关系应该成功");
    }

    @Test
    public void testDuplicateRelation() {
        // 测试重复关系处理
        productAreaRelationService.addRelation(1, 1, 1);
        
        // 尝试添加重复关系应该失败
        assertThrows(RuntimeException.class, () -> {
            productAreaRelationService.addRelation(1, 1, 2);
        }, "添加重复关系应该抛出异常");
    }

    @Test
    public void testSortOrderUpdate() {
        // 添加关系
        productAreaRelationService.addRelation(1, 1, 1);
        List<ProductAreaRelation> relations = productAreaRelationService.getByProductId(1);
        
        if (!relations.isEmpty()) {
            ProductAreaRelation relation = relations.get(0);
            
            // 测试更新排序
            boolean result = productAreaRelationService.updateSortOrder(relation.getRelationId(), 5);
            assertTrue(result, "更新排序应该成功");
        }
    }
}
