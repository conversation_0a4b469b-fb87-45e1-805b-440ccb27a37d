package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ExplanationPoint;

import java.util.List;

/**
 * 讲解点服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface ExplanationPointService extends IService<ExplanationPoint> {

    /**
     * 根据区域ID获取讲解点列表
     *
     * @param areaId 区域ID
     * @return 讲解点列表
     */
    List<ExplanationPoint> getPointsByAreaId(Integer areaId);
}
