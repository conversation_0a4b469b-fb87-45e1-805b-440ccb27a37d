<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationPointMapper">

    <!-- 根据区域ID获取讲解点列表 -->
    <select id="selectPointsByAreaId" resultType="com.tourism.miniprogram.entity.ExplanationPoint">
        SELECT point_id, area_id, point_name, point_image, sort_order, created_at, updated_at
        FROM explanation_point
        WHERE area_id = #{areaId}
        ORDER BY sort_order ASC, point_id DESC
    </select>

</mapper>
