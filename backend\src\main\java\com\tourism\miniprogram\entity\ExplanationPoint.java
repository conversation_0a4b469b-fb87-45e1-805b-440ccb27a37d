package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲解点实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("explanation_point")
@ApiModel(value = "ExplanationPoint对象", description = "讲解点信息")
public class ExplanationPoint implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "讲解点ID")
    @TableId(value = "point_id", type = IdType.AUTO)
    private Integer pointId;

    @ApiModelProperty(value = "讲解点名称")
    @TableField("point_name")
    @NotBlank(message = "讲解点名称不能为空")
    private String pointName;

    @ApiModelProperty(value = "讲解点图片URL")
    @TableField("point_image")
    @NotBlank(message = "讲解点图片不能为空")
    private String pointImage;

    @ApiModelProperty(value = "讲解点顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
