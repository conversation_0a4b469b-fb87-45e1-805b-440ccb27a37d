package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Review;
import com.tourism.miniprogram.service.ReviewService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 评价控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/reviews")
@Api(tags = "评价管理")
public class ReviewController {

    @Autowired
    private ReviewService reviewService;

    /**
     * 分页获取评价列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取评价列表", notes = "分页获取评价列表，支持按内容、用户ID、产品ID筛选")
    public Result<IPage<Review>> getReviewPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "评价内容") @RequestParam(required = false) String content,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "产品ID") @RequestParam(required = false) String productId,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<Review> page = new Page<>(current, size);
            QueryWrapper<Review> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(content)) {
                queryWrapper.like("content", content);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (StringUtils.hasText(productId)) {
                queryWrapper.eq("product_id", productId);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("review_id");

            IPage<Review> reviewPage = reviewService.page(page, queryWrapper);
            return Result.success(reviewPage);
        } catch (Exception e) {
            log.error("分页获取评价列表失败", e);
            return Result.error("获取评价列表失败");
        }
    }

    /**
     * 获取评价详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取评价详情", notes = "根据ID获取评价详细信息")
    public Result<Review> getReviewById(@ApiParam(value = "评价ID", required = true) @PathVariable Integer id) {
        try {
            Review review = reviewService.getById(id);
            if (review == null) {
                return Result.error(404, "评价不存在");
            }
            return Result.success(review);
        } catch (Exception e) {
            log.error("获取评价详情失败，id: {}", id, e);
            return Result.error("获取评价详情失败");
        }
    }

    /**
     * 创建评价
     */
    @PostMapping
    @ApiOperation(value = "创建评价", notes = "创建新的评价")
    public Result<String> createReview(@RequestBody @Valid Review review) {
        try {
            boolean success = reviewService.save(review);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建评价失败", e);
            return Result.error("创建评价失败");
        }
    }

    /**
     * 更新评价
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新评价", notes = "更新评价信息")
    public Result<String> updateReview(
            @ApiParam(value = "评价ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid Review review) {
        try {
            Review existReview = reviewService.getById(id);
            if (existReview == null) {
                return Result.error(404, "评价不存在");
            }

            review.setReviewId(id);
            boolean success = reviewService.updateById(review);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新评价失败，id: {}", id, e);
            return Result.error("更新评价失败");
        }
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除评价", notes = "删除评价")
    public Result<String> deleteReview(@ApiParam(value = "评价ID", required = true) @PathVariable Integer id) {
        try {
            Review review = reviewService.getById(id);
            if (review == null) {
                return Result.error(404, "评价不存在");
            }

            boolean success = reviewService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除评价失败，id: {}", id, e);
            return Result.error("删除评价失败");
        }
    }
}
