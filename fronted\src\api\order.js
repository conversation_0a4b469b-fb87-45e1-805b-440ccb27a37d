import request from '@/utils/request'

// 分页获取订单列表
export function getOrderPage(params) {
  return request({
    url: '/orders/page',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderById(id) {
  return request({
    url: `/orders/${id}`,
    method: 'get'
  })
}

// 根据用户ID获取订单列表
export function getOrdersByUserId(userId) {
  return request({
    url: `/orders/user/${userId}`,
    method: 'get'
  })
}

// 根据状态获取订单列表
export function getOrdersByStatus(status) {
  return request({
    url: `/orders/status/${status}`,
    method: 'get'
  })
}

// 创建订单
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'post',
    data
  })
}

// 支付订单
export function payOrder(id) {
  return request({
    url: `/orders/${id}/pay`,
    method: 'post'
  })
}

// 取消订单
export function cancelOrder(id) {
  return request({
    url: `/orders/${id}/cancel`,
    method: 'post'
  })
}

// 删除订单
export function deleteOrder(id) {
  return request({
    url: `/orders/${id}`,
    method: 'delete'
  })
}
