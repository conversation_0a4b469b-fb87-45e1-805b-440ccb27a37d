package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Product;

import java.util.List;

/**
 * 产品服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface ProductService extends IService<Product> {

    /**
     * 获取启用的产品列表
     *
     * @return 产品列表
     */
    List<Product> getEnabledProducts();

    /**
     * 根据景区ID获取产品列表
     *
     * @param scenicId 景区ID
     * @return 产品列表
     */
    List<Product> getProductsByScenicId(String scenicId);

    /**
     * 根据产品类型获取产品列表
     *
     * @param productType 产品类型
     * @return 产品列表
     */
    List<Product> getProductsByType(String productType);
}
