-- 更新讲解点表结构
-- 添加讲解点描述图片字段

USE `tourism_miniprogram`;

-- 确保 point_table 表存在
CREATE TABLE IF NOT EXISTS `point_table` (
  `point_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '讲解点ID',
  `product_id` int(11) NOT NULL COMMENT '产品ID',
  `example_image_url` varchar(500) DEFAULT NULL COMMENT '示例图片URL',
  `title` varchar(100) NOT NULL COMMENT '讲解点标题',
  `duration` varchar(50) DEFAULT NULL COMMENT '讲解时长',
  `location` varchar(255) DEFAULT NULL COMMENT '讲解点位置',
  `audio_url` varchar(500) DEFAULT NULL COMMENT '音频URL',
  `category_tags` varchar(255) DEFAULT NULL COMMENT '分类标签',
  `description` text DEFAULT NULL COMMENT '讲解点描述',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`point_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='讲解点表';

-- 添加讲解点描述图片字段（如果不存在）
ALTER TABLE point_table ADD COLUMN IF NOT EXISTS description_image_url VARCHAR(500) DEFAULT NULL COMMENT '讲解点描述图片URL';

-- 修改 duration 字段类型为 VARCHAR（如果是其他类型）
ALTER TABLE point_table MODIFY COLUMN duration VARCHAR(50) DEFAULT NULL COMMENT '讲解时长';
