package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ProductBundle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 产品组合包Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface ProductBundleMapper extends BaseMapper<ProductBundle> {

    /**
     * 获取启用的组合包列表
     *
     * @return 组合包列表
     */
    @Select("SELECT * FROM product_bundles WHERE status = 1 ORDER BY id DESC")
    List<ProductBundle> selectEnabledBundles();
}
