<template>
  <div class="batch-guide-point-form">
    <div class="page-header">
      <h2>批量创建讲解点</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <!-- 4级级联选择器 -->
      <div class="section-title">选择讲解产品</div>
      <el-form
        ref="baseFormRef"
        :model="baseForm"
        :rules="baseRules"
        label-width="120px"
        style="max-width: 800px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="省份" prop="provinceId" width="150px">
              <el-select
                v-model="baseForm.provinceId"
                placeholder="请选择省份"
                style="width: 100%"
                filterable
                clearable
                :loading="provinceLoading"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.name"
                  :value="province.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="城市" prop="cityId">
              <el-select
                v-model="baseForm.cityId"
                placeholder="请选择城市"
                style="width: 100%"
                filterable
                clearable
                :loading="cityLoading"
                :disabled="!baseForm.provinceId"
                @change="handleCityChange"
              >
                <el-option
                  v-for="city in cityList"
                  :key="city.id"
                  :label="city.name"
                  :value="city.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="景区" prop="scenicId">
              <el-select
                v-model="baseForm.scenicId"
                placeholder="请选择景区"
                style="width: 100%"
                filterable
                clearable
                :loading="scenicLoading"
                :disabled="!baseForm.cityId"
                @change="handleScenicChange"
              >
                <el-option
                  v-for="scenic in scenicList"
                  :key="scenic.id"
                  :label="scenic.title"
                  :value="scenic.scenicId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="讲解产品" prop="productId">
              <el-select
                v-model="baseForm.productId"
                placeholder="请选择讲解产品"
                style="width: 100%"
                filterable
                clearable
                :loading="productLoading"
                :disabled="!baseForm.scenicId"
              >
                <el-option
                  v-for="product in productList"
                  :key="product.productId"
                  :label="product.title"
                  :value="product.productId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="示例图片" prop="exampleImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="baseForm.exampleImageUrl" :src="baseForm.exampleImageUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
          <div class="upload-tip">这张图片将作为所有讲解点的共享示例图片</div>
        </el-form-item>
      </el-form>

      <!-- 讲解点列表 -->
      <div class="section-title">
        讲解点列表
        <el-button type="primary" size="small" @click="addGuidePoint">
          <el-icon><Plus /></el-icon>
          添加讲解点
        </el-button>
      </div>

      <div v-if="guidePoints.length === 0" class="empty-state">
        <el-empty description="暂无讲解点，请点击上方按钮添加" />
      </div>

      <div v-else class="guide-points-list">
        <el-card
          v-for="(point, index) in guidePoints"
          :key="index"
          class="guide-point-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <span>讲解点 {{ index + 1 }}</span>
              <el-button
                type="danger"
                size="small"
                :icon="Delete"
                @click="removeGuidePoint(index)"
              >
                删除
              </el-button>
            </div>
          </template>

          <el-form
            :ref="el => pointFormRefs[index] = el"
            :model="point"
            :rules="pointRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="讲解点标题" prop="title">
                  <el-input
                    v-model="point.title"
                    placeholder="请输入讲解点标题"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="讲解点位置" prop="location">
                  <el-input
                    v-model="point.location"
                    placeholder="请输入讲解点位置"
                    maxlength="255"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="讲解时长" prop="duration">
                  <el-input
                    v-model="point.duration"
                    placeholder="如：00:05:00等"
                    maxlength="50"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="分类标签" prop="categoryTags">
                  <el-input
                    v-model="point.categoryTags"
                    placeholder="多个标签用英文逗号分隔"
                    maxlength="255"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="音频文件" prop="audioUrl">
              <div class="upload-container">
                <el-input
                  v-model="point.audioUrl"
                  placeholder="音频文件URL"
                  readonly
                  style="margin-bottom: 10px"
                />
                <el-upload
                  class="audio-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeAudioUpload"
                  :http-request="(options) => handleAudioUpload(options, index)"
                  :disabled="point.audioUploadLoading"
                >
                  <el-button type="primary" :loading="point.audioUploadLoading">
                    {{ point.audioUploadLoading ? '上传中...' : '上传音频' }}
                  </el-button>
                </el-upload>
                <div class="upload-tip">支持mp3、wav、m4a格式，文件大小不超过10MB</div>
              </div>
            </el-form-item>

            <el-form-item label="描述图片" prop="descriptionImageUrl">
              <el-upload
                class="image-uploader small"
                action="#"
                :show-file-list="false"
                :before-upload="beforeDescriptionImageUpload"
                :http-request="(options) => handleDescriptionImageUpload(options, index)"
                :disabled="point.descriptionImageUploadLoading"
              >
                <img v-if="point.descriptionImageUrl" :src="point.descriptionImageUrl" class="small-image" />
                <div v-else-if="point.descriptionImageUploadLoading" class="image-uploader-loading small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  <div>上传中...</div>
                </div>
                <el-icon v-else class="image-uploader-icon small"><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
            </el-form-item>

            <el-form-item label="讲解点描述" prop="description">
              <el-input
                v-model="point.description"
                type="textarea"
                :rows="3"
                placeholder="请输入讲解点描述"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button type="primary" @click="handleSubmit" :loading="loading" size="large">
          批量创建讲解点
        </el-button>
        <el-button @click="handleReset" size="large">重置</el-button>
        <el-button @click="handleBack" size="large">取消</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { Plus, Loading, Delete } from '@element-plus/icons-vue'
import { createGuidePoint } from '@/api/guidePoint'
import { uploadImage, uploadFile } from '@/api/upload'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'
import { getScenicsByCityId } from '@/api/scenic'
import { getGuideProductPage } from '@/api/guideProduct'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const provinceLoading = ref(false)
const cityLoading = ref(false)
const scenicLoading = ref(false)
const productLoading = ref(false)
const baseFormRef = ref()
const pointFormRefs = ref([])

// 级联数据列表
const provinceList = ref([])
const cityList = ref([])
const scenicList = ref([])
const productList = ref([])

// 基础表单数据
const baseForm = reactive({
  provinceId: null,
  cityId: null,
  scenicId: null,
  productId: null,
  exampleImageUrl: ''
})

// 讲解点列表
const guidePoints = ref([])

// 基础表单验证规则
const baseRules = {
  provinceId: [
    { required: true, message: '请选择省份', trigger: 'change' }
  ],
  cityId: [
    { required: true, message: '请选择城市', trigger: 'change' }
  ],
  scenicId: [
    { required: true, message: '请选择景区', trigger: 'change' }
  ],
  productId: [
    { required: true, message: '请选择讲解产品', trigger: 'change' }
  ],
  exampleImageUrl: [
    { required: true, message: '请上传示例图片', trigger: 'change' }
  ]
}

// 讲解点验证规则
const pointRules = {
  title: [
    { required: true, message: '请输入讲解点标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ]
}

// 创建新的讲解点对象
const createNewGuidePoint = () => ({
  title: '',
  location: '',
  duration: '',
  audioUrl: '',
  categoryTags: '',
  description: '',
  descriptionImageUrl: '',
  audioUploadLoading: false,
  descriptionImageUploadLoading: false,
  status: 1
})

// 添加讲解点
const addGuidePoint = () => {
  guidePoints.value.push(createNewGuidePoint())
}

// 删除讲解点
const removeGuidePoint = async (index) => {
  if (guidePoints.value.length === 1) {
    ElMessage.warning('至少需要保留一个讲解点')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这个讲解点吗？',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    guidePoints.value.splice(index, 1)
  } catch (error) {
    // 用户取消删除
  }
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    provinceLoading.value = true
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    ElMessage.error('获取省份列表失败')
    console.error(error)
  } finally {
    provinceLoading.value = false
  }
}

// 获取城市列表
const fetchCityList = async (provinceId) => {
  try {
    cityLoading.value = true
    const { data } = await getCityList(provinceId)
    cityList.value = data || []
  } catch (error) {
    ElMessage.error('获取城市列表失败')
    console.error(error)
  } finally {
    cityLoading.value = false
  }
}

// 获取景区列表
const fetchScenicList = async (cityId) => {
  try {
    scenicLoading.value = true
    const { data } = await getScenicsByCityId(cityId)
    scenicList.value = data.records || data || []
  } catch (error) {
    ElMessage.error('获取景区列表失败')
    console.error(error)
  } finally {
    scenicLoading.value = false
  }
}

// 获取讲解产品列表
const fetchProductList = async (scenicId) => {
  try {
    productLoading.value = true
    const { data } = await getGuideProductPage({
      current: 1,
      size: 1000, // 获取足够多的数据，避免分页问题
      scenicId: scenicId,
      status: 1 // 只获取启用的产品
    })
    productList.value = data.records || []
  } catch (error) {
    ElMessage.error('获取讲解产品列表失败')
    console.error(error)
  } finally {
    productLoading.value = false
  }
}

// 省份变化处理
const handleProvinceChange = (provinceId) => {
  baseForm.cityId = null
  baseForm.scenicId = null
  baseForm.productId = null
  cityList.value = []
  scenicList.value = []
  productList.value = []

  if (provinceId) {
    fetchCityList(provinceId)
  }
}

// 城市变化处理
const handleCityChange = (cityId) => {
  baseForm.scenicId = null
  baseForm.productId = null
  scenicList.value = []
  productList.value = []

  if (cityId) {
    fetchScenicList(cityId)
  }
}

// 景区变化处理
const handleScenicChange = (scenicId) => {
  baseForm.productId = null
  productList.value = []

  if (scenicId) {
    fetchProductList(scenicId)
  }
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/gif' || file.type === 'image/webp'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG/GIF/WEBP 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 示例图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      baseForm.exampleImageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 音频上传前验证
const beforeAudioUpload = (file) => {
  const isAudio = file.type.startsWith('audio/') || 
                  file.type === 'audio/mp3' || 
                  file.type === 'audio/wav' || 
                  file.type === 'audio/m4a'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('音频文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 音频上传
const handleAudioUpload = async (options, index) => {
  const { file } = options

  try {
    guidePoints.value[index].audioUploadLoading = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '音频上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadFile(file, (progress) => {
      loadingInstance.setText(`音频上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      guidePoints.value[index].audioUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('音频上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('音频上传失败:', error)
    ElMessage.error(error.message || '音频上传失败，请重试')
  } finally {
    guidePoints.value[index].audioUploadLoading = false
  }
}

// 描述图片上传前验证
const beforeDescriptionImageUpload = beforeImageUpload

// 描述图片上传
const handleDescriptionImageUpload = async (options, index) => {
  const { file } = options

  try {
    guidePoints.value[index].descriptionImageUploadLoading = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      guidePoints.value[index].descriptionImageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    guidePoints.value[index].descriptionImageUploadLoading = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!baseFormRef.value) return

  try {
    // 验证基础表单
    await baseFormRef.value.validate()

    // 验证所有讲解点表单
    for (let i = 0; i < pointFormRefs.value.length; i++) {
      if (pointFormRefs.value[i]) {
        await pointFormRefs.value[i].validate()
      }
    }

    if (guidePoints.value.length === 0) {
      ElMessage.error('请至少添加一个讲解点')
      return
    }

    loading.value = true

    // 批量创建讲解点
    const promises = guidePoints.value.map(point => {
      const guidePointData = {
        ...point,
        productId: baseForm.productId,
        exampleImageUrl: baseForm.exampleImageUrl
      }
      // 移除上传状态字段
      delete guidePointData.audioUploadLoading
      delete guidePointData.descriptionImageUploadLoading
      
      return createGuidePoint(guidePointData)
    })

    await Promise.all(promises)
    
    ElMessage.success(`成功创建 ${guidePoints.value.length} 个讲解点`)
    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error('创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (baseFormRef.value) {
    baseFormRef.value.resetFields()
  }
  // 清空级联数据
  cityList.value = []
  scenicList.value = []
  productList.value = []
  guidePoints.value = []
  addGuidePoint() // 添加一个默认讲解点
}

// 返回列表
const handleBack = () => {
  router.push('/guide-point/list')
}

// 初始化
onMounted(() => {
  fetchProvinceList()
  addGuidePoint() // 默认添加一个讲解点
})
</script>

<style scoped>
.batch-guide-point-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 30px 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.empty-state {
  margin: 40px 0;
}

.guide-points-list {
  margin-bottom: 30px;
}

.guide-point-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 图片上传样式 */
.image-uploader .image {
  width: 300px;
  height: 100px;
  display: block;
  object-fit: cover;
}

.image-uploader.small .small-image {
  width: 150px;
  height: 80px;
  display: block;
  object-fit: cover;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-uploader-icon.small {
  width: 150px;
  height: 80px;
  line-height: 80px;
  font-size: 20px;
}

.image-uploader-loading {
  width: 300px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading.small {
  width: 150px;
  height: 80px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.upload-container {
  width: 100%;
}

.audio-uploader {
  margin-bottom: 5px;
}
</style>
