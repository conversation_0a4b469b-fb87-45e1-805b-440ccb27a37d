<template>
  <div class="carousel-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑轮播图' : '新增轮播图' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="副标题" prop="subtitle">
          <el-input
            v-model="form.subtitle"
            placeholder="请输入副标题"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="轮播图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.image" :src="form.image" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB，建议尺寸：1200x400</div>
        </el-form-item>

        <el-form-item label="关联景区" prop="scenicId">
          <el-input
            v-model="form.scenicId"
            placeholder="请输入关联的景区ID（可选）"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="所属省份" prop="provinceId">
          <el-select
            v-model="form.provinceId"
            placeholder="请选择省份（可选）"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="province in provinceList"
              :key="province.id"
              :label="province.name"
              :value="province.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option label="首页轮播" value="home" />
            <el-option label="活动轮播" value="activity" />
            <el-option label="推荐轮播" value="recommend" />
          </el-select>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { getCarouselInfo, createCarousel, updateCarousel } from '@/api/carousel'
import { getProvinceList } from '@/api/province'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const formRef = ref()
const provinceList = ref([])

const form = reactive({
  title: '',
  subtitle: '',
  image: '',
  scenicId: '',
  provinceId: '',
  type: 'home',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请上传轮播图片', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    // 显示上传进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用上传API
    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      // 使用预览URL作为图片地址
      form.image = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('图片上传成功')

      // 打印上传结果信息（用于调试）
      console.log('图片上传成功:', {
        fileId: response.data.fileId,
        originalFileName: response.data.originalFileName,
        fileSize: response.data.fileSize,
        fileType: response.data.fileType,
        previewUrl: response.data.previewUrl,
        downloadUrl: response.data.downloadUrl,
        cdnUrl: response.data.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getCarouselInfo(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取轮播图详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateCarousel(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createCarousel(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/carousel/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.carousel-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 300px;
  height: 100px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-uploader-loading {
  width: 300px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}
</style>
