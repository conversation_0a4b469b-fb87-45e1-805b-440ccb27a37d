import request from '@/utils/request'

// 分页获取激活码列表
export function getActivationCodePage(params) {
  return request({
    url: '/activation-codes/page',
    method: 'get',
    params
  })
}

// 获取激活码列表
export function getActivationCodeList() {
  return request({
    url: '/activation-codes',
    method: 'get'
  })
}

// 获取激活码详情
export function getActivationCodeById(id) {
  return request({
    url: `/activation-codes/${id}`,
    method: 'get'
  })
}

// 根据编码获取激活码
export function getActivationCodeByCode(code) {
  return request({
    url: `/activation-codes/code/${code}`,
    method: 'get'
  })
}

// 批量生成激活码
export function generateActivationCodes(params) {
  return request({
    url: '/activation-codes/generate',
    method: 'post',
    params
  })
}

// 创建激活码
export function createActivationCode(data) {
  return request({
    url: '/activation-codes',
    method: 'post',
    data
  })
}

// 更新激活码
export function updateActivationCode(id, data) {
  return request({
    url: `/activation-codes/${id}`,
    method: 'put',
    data
  })
}

// 删除激活码
export function deleteActivationCode(id) {
  return request({
    url: `/activation-codes/${id}`,
    method: 'delete'
  })
}
