package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.ExplanationArea;
import com.tourism.miniprogram.mapper.ExplanationAreaMapper;
import com.tourism.miniprogram.service.ExplanationAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲解区域服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class ExplanationAreaServiceImpl extends ServiceImpl<ExplanationAreaMapper, ExplanationArea> implements ExplanationAreaService {

    @Override
    public List<ExplanationArea> getEnabledAreas() {
        try {
            return baseMapper.selectList(null);
        } catch (Exception e) {
            log.error("获取启用区域列表失败", e);
            throw new RuntimeException("获取区域列表失败");
        }
    }
}
