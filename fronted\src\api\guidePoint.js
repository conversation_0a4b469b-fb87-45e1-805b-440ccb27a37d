import request from '@/utils/request'

// 分页获取讲解点列表
export function getGuidePointPage(params) {
  return request({
    url: '/guide-points/page',
    method: 'get',
    params
  })
}

// 获取讲解点详情
export function getGuidePointById(id) {
  return request({
    url: `/guide-points/${id}`,
    method: 'get'
  })
}

// 创建讲解点
export function createGuidePoint(data) {
  return request({
    url: '/guide-points',
    method: 'post',
    data
  })
}

// 更新讲解点
export function updateGuidePoint(id, data) {
  return request({
    url: `/guide-points/${id}`,
    method: 'put',
    data
  })
}

// 删除讲解点
export function deleteGuidePoint(id) {
  return request({
    url: `/guide-points/${id}`,
    method: 'delete'
  })
}
