package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ExplanationArea;

import java.util.List;

/**
 * 讲解区域服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
public interface ExplanationAreaService extends IService<ExplanationArea> {

    /**
     * 获取所有启用的区域列表
     *
     * @return 区域列表
     */
    List<ExplanationArea> getEnabledAreas();
}
