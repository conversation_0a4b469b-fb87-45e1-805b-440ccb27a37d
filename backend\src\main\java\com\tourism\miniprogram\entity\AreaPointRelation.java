package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 区域讲解点关系实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("area_point_relation")
@ApiModel(value = "AreaPointRelation对象", description = "区域讲解点关系信息")
public class AreaPointRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关系ID")
    @TableId(value = "relation_id", type = IdType.AUTO)
    private Integer relationId;

    @ApiModelProperty(value = "区域ID")
    @TableField("area_id")
    @NotNull(message = "区域ID不能为空")
    private Integer areaId;

    @ApiModelProperty(value = "讲解点ID")
    @TableField("point_id")
    @NotNull(message = "讲解点ID不能为空")
    private Integer pointId;

    @ApiModelProperty(value = "讲解点在区域中的顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
