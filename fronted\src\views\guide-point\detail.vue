<template>
  <div class="guide-point-detail">
    <div class="page-header">
      <h2>讲解点详情</h2>
      <div>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="讲解点ID">
          {{ detail.pointId }}
        </el-descriptions-item>
        <el-descriptions-item label="产品ID">
          {{ detail.productId }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解点标题">
          {{ detail.title }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解点位置">
          {{ detail.location }}
        </el-descriptions-item>
        <el-descriptions-item label="讲解时长">
          {{ detail.duration }}
        </el-descriptions-item>
        <el-descriptions-item label="分类标签">
          {{ detail.categoryTags }}
        </el-descriptions-item>
        <el-descriptions-item label="示例图片" :span="2">
          <el-image
            v-if="detail.exampleImageUrl"
            :src="detail.exampleImageUrl"
            style="width: 300px; height: 100px"
            fit="cover"
            :preview-src-list="[detail.exampleImageUrl]"
          />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="音频URL" :span="2">
          <a v-if="detail.audioUrl" :href="detail.audioUrl" target="_blank">
            {{ detail.audioUrl }}
          </a>
          <span v-else>暂无音频</span>
        </el-descriptions-item>
        <el-descriptions-item label="讲解点描述" :span="2">
          <div class="content-text">{{ detail.description || '暂无描述' }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
            {{ detail.status === 1 ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sort }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ detail.createdAt }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ detail.updatedAt }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getGuidePointById } from '@/api/guidePoint'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  loading.value = true
  try {
    const { data } = await getGuidePointById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取讲解点详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 编辑
const handleEdit = () => {
  router.push(`/guide-point/edit/${route.params.id}`)
}

// 返回列表
const handleBack = () => {
  router.push('/guide-point/list')
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.guide-point-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
}
</style>
