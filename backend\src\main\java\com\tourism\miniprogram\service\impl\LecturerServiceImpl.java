package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Lecturer;
import com.tourism.miniprogram.mapper.LecturerMapper;
import com.tourism.miniprogram.service.LecturerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 讲师服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class LecturerServiceImpl extends ServiceImpl<LecturerMapper, Lecturer> implements LecturerService {

    @Override
    public List<Lecturer> getEnabledLecturers() {
        try {
            return baseMapper.selectEnabledLecturers();
        } catch (Exception e) {
            log.error("获取讲师列表失败", e);
            throw new RuntimeException("获取讲师列表失败");
        }
    }
}
