package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Scenic;

/**
 * 景区服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface ScenicService extends IService<Scenic> {

    /**
     * 分页查询推荐景区列表
     *
     * @param page       当前页
     * @param limit      每页大小
     * @param provinceId 省份ID
     * @param cityId     城市ID
     * @return 景区分页列表
     */
    IPage<Scenic> getRecommendScenics(Long page, Long limit, Integer provinceId, Integer cityId);

    /**
     * 根据景区ID获取景区详情
     *
     * @param scenicId 景区ID
     * @return 景区详情
     */
    Scenic getScenicByScenicId(String scenicId);
}
