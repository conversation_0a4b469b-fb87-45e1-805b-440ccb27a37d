package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 使用记录实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("usage_records")
@ApiModel(value = "UsageRecord对象", description = "使用记录信息")
public class UsageRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "使用的卡券ID")
    @TableField("coupon_id")
    @NotNull(message = "卡券ID不能为空")
    private Integer couponId;

    @ApiModelProperty(value = "使用用户ID")
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "使用的景区ID")
    @TableField("scenic_id")
    @NotNull(message = "景区ID不能为空")
    private Integer scenicId;

    @ApiModelProperty(value = "使用时间")
    @TableField("used_at")
    @NotNull(message = "使用时间不能为空")
    private LocalDateTime usedAt;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
