package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.Scenic;
import com.tourism.miniprogram.mapper.ScenicMapper;
import com.tourism.miniprogram.service.ScenicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 景区服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@Service
public class ScenicServiceImpl extends ServiceImpl<ScenicMapper, Scenic> implements ScenicService {

    @Override
    public IPage<Scenic> getRecommendScenics(Long page, Long limit, Integer provinceId, Integer cityId) {
        try {
            Page<Scenic> pageObj = new Page<>(page, limit);
            return baseMapper.selectRecommendScenics(pageObj, provinceId, cityId);
        } catch (Exception e) {
            log.error("获取推荐景区列表失败，page: {}, limit: {}, provinceId: {}, cityId: {}", 
                     page, limit, provinceId, cityId, e);
            throw new RuntimeException("获取推荐景区列表失败");
        }
    }

    @Override
    public Scenic getScenicByScenicId(String scenicId) {
        try {
            if (scenicId == null || scenicId.trim().isEmpty()) {
                throw new IllegalArgumentException("景区ID不能为空");
            }
            return baseMapper.selectByScenicId(scenicId);
        } catch (Exception e) {
            log.error("根据景区ID获取景区详情失败，scenicId: {}", scenicId, e);
            throw new RuntimeException("获取景区详情失败");
        }
    }
}
