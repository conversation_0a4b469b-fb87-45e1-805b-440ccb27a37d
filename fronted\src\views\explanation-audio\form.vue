<template>
  <div class="explanation-audio-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解音频' : '新增讲解音频' }}</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="音频名称" prop="audioName">
          <el-input
            v-model="form.audioName"
            placeholder="请输入音频名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="关联讲解点" prop="pointId">
          <el-select
            v-model="form.pointId"
            placeholder="请选择关联讲解点"
            style="width: 100%"
            filterable
            clearable
            :loading="pointLoading"
          >
            <el-option
              v-for="point in pointList"
              :key="point.pointId"
              :label="`${point.pointName} (ID: ${point.pointId})`"
              :value="point.pointId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="音频关联图片" prop="audioImage">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="imageUploadLoading"
          >
            <img v-if="form.audioImage" :src="form.audioImage" class="image" />
            <div v-else-if="imageUploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="音频时长(秒)" prop="duration">
          <el-input-number
            v-model="form.duration"
            :min="1"
            :max="3600"
            placeholder="请输入音频时长"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="音频文件" prop="audioUrl">
          <el-upload
            class="audio-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeAudioUpload"
            :http-request="handleAudioUpload"
            :disabled="audioUploadLoading"
          >
            <el-button :loading="audioUploadLoading" type="primary">
              <el-icon><Upload /></el-icon>
              {{ form.audioUrl ? '重新上传音频' : '上传音频文件' }}
            </el-button>
          </el-upload>
          <div v-if="form.audioUrl" class="audio-info">
            <div class="audio-name">已上传音频文件</div>
            <audio :src="form.audioUrl" controls style="width: 100%; margin-top: 8px;"></audio>
          </div>
          <div class="upload-tip">只能上传mp3/wav/m4a文件，且不超过10MB</div>
        </el-form-item>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Loading, Upload } from '@element-plus/icons-vue'
import { 
  getExplanationAudioById, 
  createExplanationAudio, 
  updateExplanationAudio 
} from '@/api/explanationAudio'
import { getExplanationPointPage } from '@/api/explanationPoint'
import { uploadImage, uploadAudio } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const imageUploadLoading = ref(false)
const audioUploadLoading = ref(false)
const pointLoading = ref(false)
const formRef = ref()
const pointList = ref([])

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const form = reactive({
  audioName: '',
  pointId: null,
  audioImage: '',
  duration: null,
  audioUrl: '',
  sortOrder: 0
})

// 验证规则
const rules = {
  audioName: [
    { required: true, message: '请输入音频名称', trigger: 'blur' },
    { min: 1, max: 100, message: '音频名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  pointId: [
    { required: true, message: '请选择关联讲解点', trigger: 'change' }
  ],
  duration: [
    { required: true, message: '请输入音频时长', trigger: 'blur' },
    { type: 'number', min: 1, max: 3600, message: '音频时长必须在 1 到 3600 秒之间', trigger: 'blur' }
  ],
  audioUrl: [
    { required: true, message: '请上传音频文件', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 获取讲解点列表
const fetchPointList = async () => {
  try {
    pointLoading.value = true
    const { data } = await getExplanationPointPage({
      current: 1,
      size: 1000
    })
    pointList.value = data.records || []
  } catch (error) {
    ElMessage.error('获取讲解点列表失败')
    console.error(error)
  } finally {
    pointLoading.value = false
  }
}

// 获取详情数据（编辑模式）
const fetchDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const { data } = await getExplanationAudioById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取讲解音频详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isImage = /^image\/(jpeg|jpg|png|gif|webp)$/i.test(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传 JPG/PNG/GIF/WEBP 格式的图片!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 音频上传前验证
const beforeAudioUpload = (file) => {
  const isAudio = /^audio\/(mpeg|mp3|wav|x-wav|m4a)$/i.test(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isAudio) {
    ElMessage.error('只能上传 MP3/WAV/M4A 格式的音频文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('上传音频文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 处理图片上传
const handleImageUpload = async (options) => {
  try {
    imageUploadLoading.value = true
    const url = await uploadImage(options.file)
    form.audioImage = url
    ElMessage.success('图片上传成功')
  } catch (error) {
    ElMessage.error('图片上传失败')
    console.error(error)
  } finally {
    imageUploadLoading.value = false
  }
}

// 处理音频上传
const handleAudioUpload = async (options) => {
  try {
    audioUploadLoading.value = true
    const url = await uploadAudio(options.file)
    form.audioUrl = url
    ElMessage.success('音频上传成功')
  } catch (error) {
    ElMessage.error('音频上传失败')
    console.error(error)
  } finally {
    audioUploadLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateExplanationAudio(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createExplanationAudio(form)
      ElMessage.success('创建成功')
    }
    
    router.back()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 初始化
onMounted(() => {
  fetchPointList()
  fetchDetail()
})
</script>

<style scoped>
.explanation-audio-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.image {
  width: 178px;
  height: 178px;
  object-fit: cover;
}

.image-uploader-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.audio-uploader {
  width: 100%;
}

.audio-info {
  margin-top: 10px;
}

.audio-name {
  color: #67c23a;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
