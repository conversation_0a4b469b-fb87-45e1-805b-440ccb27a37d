package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 产品区域关系实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("product_area_relation")
@ApiModel(value = "ProductAreaRelation对象", description = "产品区域关系信息")
public class ProductAreaRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "关系ID")
    @TableId(value = "relation_id", type = IdType.AUTO)
    private Integer relationId;

    @ApiModelProperty(value = "产品ID")
    @TableField("product_id")
    @NotNull(message = "产品ID不能为空")
    private Integer productId;

    @ApiModelProperty(value = "区域ID")
    @TableField("area_id")
    @NotNull(message = "区域ID不能为空")
    private Integer areaId;

    @ApiModelProperty(value = "区域在产品中的顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
