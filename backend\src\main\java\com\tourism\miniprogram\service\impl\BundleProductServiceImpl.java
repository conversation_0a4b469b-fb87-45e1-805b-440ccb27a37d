package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.BundleProduct;
import com.tourism.miniprogram.mapper.BundleProductMapper;
import com.tourism.miniprogram.service.BundleProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 组合包产品关联服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Service
public class BundleProductServiceImpl extends ServiceImpl<BundleProductMapper, BundleProduct> implements BundleProductService {

    @Override
    public List<BundleProduct> getByBundleId(Integer bundleId) {
        try {
            return baseMapper.selectByBundleId(bundleId);
        } catch (Exception e) {
            log.error("根据组合包ID获取产品关联列表失败，bundleId: {}", bundleId, e);
            throw new RuntimeException("根据组合包ID获取产品关联列表失败");
        }
    }

    @Override
    public List<BundleProduct> getByProductId(Integer productId) {
        try {
            return baseMapper.selectByProductId(productId);
        } catch (Exception e) {
            log.error("根据产品ID获取组合包关联列表失败，productId: {}", productId, e);
            throw new RuntimeException("根据产品ID获取组合包关联列表失败");
        }
    }

    @Override
    @Transactional
    public boolean batchAddBundleProducts(Integer bundleId, List<Integer> productIds, List<Integer> quantities, List<Integer> scenicIds) {
        try {
            if (productIds.size() != quantities.size() || productIds.size() != scenicIds.size()) {
                log.error("参数列表长度不一致");
                return false;
            }

            List<BundleProduct> bundleProducts = new ArrayList<>();
            for (int i = 0; i < productIds.size(); i++) {
                BundleProduct bundleProduct = new BundleProduct();
                bundleProduct.setBundleId(bundleId);
                bundleProduct.setProductId(productIds.get(i));
                bundleProduct.setQuantity(quantities.get(i));
                bundleProduct.setScenicId(scenicIds.get(i));
                bundleProduct.setCreatedAt(LocalDateTime.now());
                
                bundleProducts.add(bundleProduct);
            }

            boolean success = saveBatch(bundleProducts);
            if (success) {
                log.info("批量添加组合包产品关联成功，bundleId: {}, 数量: {}", bundleId, productIds.size());
                return true;
            } else {
                log.error("批量添加组合包产品关联失败");
                return false;
            }

        } catch (Exception e) {
            log.error("批量添加组合包产品关联失败，bundleId: {}", bundleId, e);
            throw new RuntimeException("批量添加组合包产品关联失败");
        }
    }

    @Override
    @Transactional
    public boolean removeByBundleId(Integer bundleId) {
        try {
            QueryWrapper<BundleProduct> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("bundle_id", bundleId);
            
            boolean success = remove(queryWrapper);
            if (success) {
                log.info("删除组合包的所有产品关联成功，bundleId: {}", bundleId);
                return true;
            } else {
                log.error("删除组合包的所有产品关联失败，bundleId: {}", bundleId);
                return false;
            }

        } catch (Exception e) {
            log.error("删除组合包的所有产品关联失败，bundleId: {}", bundleId, e);
            throw new RuntimeException("删除组合包的所有产品关联失败");
        }
    }
}
