-- 创建数据库
CREATE DATABASE IF NOT EXISTS `tourism_miniprogram` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `tourism_miniprogram`;

-- 创建省份表
CREATE TABLE IF NOT EXISTS `provinces` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '省份名称',
  `code` varchar(10) NOT NULL COMMENT '省份代码',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='省份表';

-- 创建城市表
CREATE TABLE IF NOT EXISTS `cities` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '城市名称',
  `province_id` int(11) NOT NULL COMMENT '省份ID',
  `code` varchar(10) NOT NULL COMMENT '城市代码',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_code` (`code`),
  KEY `idx_province_id` (`province_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  CONSTRAINT `fk_cities_province` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='城市表';

-- 创建景区表
CREATE TABLE IF NOT EXISTS `scenics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `scenic_id` varchar(100) NOT NULL COMMENT '景区唯一标识',
  `title` varchar(200) NOT NULL COMMENT '景区标题',
  `subtitle` varchar(500) DEFAULT NULL COMMENT '景区副标题',
  `description` text COMMENT '景区描述',
  `image` varchar(500) DEFAULT NULL COMMENT '主图片',
  `images` json DEFAULT NULL COMMENT '图片集合',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '价格',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `open_time` varchar(100) DEFAULT NULL COMMENT '开放时间',
  `address` varchar(500) DEFAULT NULL COMMENT '地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `province_id` int(11) NOT NULL COMMENT '省份ID',
  `city_id` int(11) NOT NULL COMMENT '城市ID',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_scenic_id` (`scenic_id`),
  KEY `idx_province_id` (`province_id`),
  KEY `idx_city_id` (`city_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  CONSTRAINT `fk_scenics_province` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`),
  CONSTRAINT `fk_scenics_city` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='景区表';

-- 创建轮播图表
CREATE TABLE IF NOT EXISTS `carousels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '标题',
  `subtitle` varchar(500) DEFAULT NULL COMMENT '副标题',
  `image` varchar(500) NOT NULL COMMENT '图片URL',
  `scenic_id` varchar(100) DEFAULT NULL COMMENT '关联景区ID',
  `province_id` int(11) DEFAULT NULL COMMENT '省份ID',
  `type` varchar(20) DEFAULT 'home' COMMENT '类型：home-首页',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_province_id` (`province_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort`),
  CONSTRAINT `fk_carousels_province` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图表';

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入测试数据

-- 插入省份数据
INSERT INTO `provinces` (`name`, `code`, `sort`, `status`) VALUES
('北京市', 'BJ', 1, 1),
('上海市', 'SH', 2, 1),
('广东省', 'GD', 3, 1),
('浙江省', 'ZJ', 4, 1),
('江苏省', 'JS', 5, 1),
('四川省', 'SC', 6, 1),
('云南省', 'YN', 7, 1),
('西藏自治区', 'XZ', 8, 1);

-- 插入城市数据
INSERT INTO `cities` (`name`, `province_id`, `code`, `sort`, `status`) VALUES
('北京市', 1, 'BJ01', 1, 1),
('上海市', 2, 'SH01', 1, 1),
('广州市', 3, 'GD01', 1, 1),
('深圳市', 3, 'GD02', 2, 1),
('杭州市', 4, 'ZJ01', 1, 1),
('宁波市', 4, 'ZJ02', 2, 1),
('南京市', 5, 'JS01', 1, 1),
('苏州市', 5, 'JS02', 2, 1),
('成都市', 6, 'SC01', 1, 1),
('昆明市', 7, 'YN01', 1, 1),
('拉萨市', 8, 'XZ01', 1, 1);
