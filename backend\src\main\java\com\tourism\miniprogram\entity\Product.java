package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 产品实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("products")
@ApiModel(value = "Product对象", description = "产品信息")
public class Product implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "产品ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联景区ID")
    @TableField("scenic_id")
    @NotBlank(message = "景区ID不能为空")
    private String scenicId;

    @ApiModelProperty(value = "产品名称")
    @TableField("name")
    @NotBlank(message = "产品名称不能为空")
    private String name;

    @ApiModelProperty(value = "产品描述")
    @TableField("description")
    private String description;

    @ApiModelProperty(value = "销售价格")
    @TableField("price")
    @NotNull(message = "价格不能为空")
    @DecimalMin(value = "0.00", message = "价格不能小于0")
    private BigDecimal price;

    @ApiModelProperty(value = "激活后有效期(小时)")
    @TableField("validity_hours")
    @NotNull(message = "有效期不能为空")
    private Integer validityHours;

    @ApiModelProperty(value = "是否需要激活码")
    @TableField("require_activation")
    @NotNull(message = "是否需要激活码不能为空")
    private Boolean requireActivation;

    @ApiModelProperty(value = "产品类型：single-单品，bundle-组合包")
    @TableField("product_type")
    @NotBlank(message = "产品类型不能为空")
    private String productType;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
