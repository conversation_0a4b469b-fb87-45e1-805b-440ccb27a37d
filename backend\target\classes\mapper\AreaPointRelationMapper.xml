<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.AreaPointRelationMapper">

    <!-- 根据区域ID获取讲解点关系列表 -->
    <select id="selectByAreaId" resultType="com.tourism.miniprogram.entity.AreaPointRelation">
        SELECT relation_id, area_id, point_id, sort_order, created_at
        FROM area_point_relation
        WHERE area_id = #{areaId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 根据讲解点ID获取区域关系列表 -->
    <select id="selectByPointId" resultType="com.tourism.miniprogram.entity.AreaPointRelation">
        SELECT relation_id, area_id, point_id, sort_order, created_at
        FROM area_point_relation
        WHERE point_id = #{pointId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 批量插入关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO area_point_relation (area_id, point_id, sort_order)
        VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.areaId}, #{item.pointId}, #{item.sortOrder})
        </foreach>
    </insert>

    <!-- 根据区域ID删除所有关系 -->
    <delete id="deleteByAreaId">
        DELETE FROM area_point_relation WHERE area_id = #{areaId}
    </delete>

    <!-- 根据讲解点ID删除所有关系 -->
    <delete id="deleteByPointId">
        DELETE FROM area_point_relation WHERE point_id = #{pointId}
    </delete>

    <!-- 更新排序 -->
    <update id="updateSortOrder">
        UPDATE area_point_relation 
        SET sort_order = #{sortOrder}
        WHERE relation_id = #{relationId}
    </update>

</mapper>
