package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ActivationCode;

import java.util.List;

/**
 * 激活码服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface ActivationCodeService extends IService<ActivationCode> {

    /**
     * 根据景区ID获取激活码列表
     *
     * @param scenicId 景区ID
     * @return 激活码列表
     */
    List<ActivationCode> getActivationCodesByScenicId(Integer scenicId);

    /**
     * 根据状态获取激活码列表
     *
     * @param status 激活码状态
     * @return 激活码列表
     */
    List<ActivationCode> getActivationCodesByStatus(String status);

    /**
     * 根据激活码查找
     *
     * @param code 激活码
     * @return 激活码对象
     */
    ActivationCode getByCode(String code);

    /**
     * 生成激活码
     *
     * @return 激活码
     */
    String generateActivationCode();

    /**
     * 批量生成激活码
     *
     * @param count 数量
     * @param scenicId 景区ID
     * @param productId 产品ID
     * @param bundleId 组合包ID
     * @return 是否成功
     */
    boolean batchGenerateActivationCodes(Integer count, Integer scenicId, Integer productId, Integer bundleId);
}
