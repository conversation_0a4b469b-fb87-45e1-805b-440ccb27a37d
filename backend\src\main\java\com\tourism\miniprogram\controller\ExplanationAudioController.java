package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ExplanationAudio;
import com.tourism.miniprogram.service.ExplanationAudioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 讲解音频控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/explanation-audios")
@Api(tags = "讲解音频管理")
public class ExplanationAudioController {

    @Autowired
    private ExplanationAudioService explanationAudioService;

    /**
     * 分页获取讲解音频列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲解音频列表", notes = "分页获取讲解音频列表，支持按音频名称、讲解点ID筛选")
    public Result<IPage<ExplanationAudio>> getExplanationAudioPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "音频名称") @RequestParam(required = false) String audioName,
            @ApiParam(value = "讲解点ID") @RequestParam(required = false) Integer pointId) {
        try {
            Page<ExplanationAudio> page = new Page<>(current, size);
            QueryWrapper<ExplanationAudio> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(audioName)) {
                queryWrapper.like("audio_name", audioName);
            }
            if (pointId != null) {
                queryWrapper.eq("point_id", pointId);
            }
            queryWrapper.orderByAsc("sort_order").orderByDesc("audio_id");

            IPage<ExplanationAudio> audioPage = explanationAudioService.page(page, queryWrapper);
            return Result.success(audioPage);
        } catch (Exception e) {
            log.error("分页获取讲解音频列表失败", e);
            return Result.error("获取讲解音频列表失败");
        }
    }

    /**
     * 获取讲解音频详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲解音频详情", notes = "根据ID获取讲解音频详细信息")
    public Result<ExplanationAudio> getExplanationAudioById(@ApiParam(value = "音频ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationAudio audio = explanationAudioService.getById(id);
            if (audio == null) {
                return Result.error(404, "讲解音频不存在");
            }
            return Result.success(audio);
        } catch (Exception e) {
            log.error("获取讲解音频详情失败，id: {}", id, e);
            return Result.error("获取讲解音频详情失败");
        }
    }

    /**
     * 根据讲解点ID获取音频列表
     * @deprecated 请使用 /relations/point-audio/point/{pointId} 接口
     */
    @GetMapping("/point/{pointId}")
    @ApiOperation(value = "根据讲解点ID获取音频列表", notes = "根据讲解点ID获取该讲解点的所有音频。建议使用关系管理API：/relations/point-audio/point/{pointId}")
    @Deprecated
    public Result<String> getAudiosByPointId(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer pointId) {
        return Result.error("此接口已废弃，请使用关系管理API：/relations/point-audio/point/" + pointId);
    }

    /**
     * 创建讲解音频
     */
    @PostMapping
    @ApiOperation(value = "创建讲解音频", notes = "创建新的讲解音频")
    public Result<String> createExplanationAudio(@RequestBody @Valid ExplanationAudio audio) {
        try {
            boolean success = explanationAudioService.save(audio);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲解音频失败", e);
            return Result.error("创建讲解音频失败");
        }
    }

    /**
     * 更新讲解音频
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲解音频", notes = "更新讲解音频信息")
    public Result<String> updateExplanationAudio(
            @ApiParam(value = "音频ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid ExplanationAudio audio) {
        try {
            ExplanationAudio existAudio = explanationAudioService.getById(id);
            if (existAudio == null) {
                return Result.error(404, "讲解音频不存在");
            }

            audio.setAudioId(id);
            boolean success = explanationAudioService.updateById(audio);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲解音频失败，id: {}", id, e);
            return Result.error("更新讲解音频失败");
        }
    }

    /**
     * 删除讲解音频
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲解音频", notes = "删除讲解音频")
    public Result<String> deleteExplanationAudio(@ApiParam(value = "音频ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationAudio audio = explanationAudioService.getById(id);
            if (audio == null) {
                return Result.error(404, "讲解音频不存在");
            }

            boolean success = explanationAudioService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲解音频失败，id: {}", id, e);
            return Result.error("删除讲解音频失败");
        }
    }
}
