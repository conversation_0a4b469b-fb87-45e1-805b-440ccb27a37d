import request from '@/utils/request'

// 分页获取使用记录列表
export function getUsageRecordPage(params) {
  return request({
    url: '/usage-records/page',
    method: 'get',
    params
  })
}

// 获取使用记录列表
export function getUsageRecordList() {
  return request({
    url: '/usage-records',
    method: 'get'
  })
}

// 获取使用记录详情
export function getUsageRecordById(id) {
  return request({
    url: `/usage-records/${id}`,
    method: 'get'
  })
}

// 根据用户ID获取使用记录
export function getUsageRecordsByUserId(userId) {
  return request({
    url: `/usage-records/user/${userId}`,
    method: 'get'
  })
}

// 创建使用记录
export function createUsageRecord(data) {
  return request({
    url: '/usage-records',
    method: 'post',
    data
  })
}

// 更新使用记录
export function updateUsageRecord(id, data) {
  return request({
    url: `/usage-records/${id}`,
    method: 'put',
    data
  })
}

// 删除使用记录
export function deleteUsageRecord(id) {
  return request({
    url: `/usage-records/${id}`,
    method: 'delete'
  })
}
