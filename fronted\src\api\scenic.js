import request from '@/utils/request'

/**
 * 获取推荐景区列表
 * @param {Object} params 查询参数
 * @param {number} params.provinceId 省份ID
 * @param {number} params.cityId 城市ID
 * @param {number} params.page 当前页
 * @param {number} params.limit 每页大小
 */
export function getRecommendScenics(params) {
  return request({
    url: '/scenics/recommend',
    method: 'get',
    params
  })
}

/**
 * 分页获取景区列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @param {string} params.title 景区标题
 * @param {number} params.provinceId 省份ID
 * @param {number} params.cityId 城市ID
 */
export function getScenicPage(params) {
  return request({
    url: '/scenics/page',
    method: 'get',
    params
  })
}

/**
 * 根据景区ID获取景区详情
 * @param {string} scenicId 景区ID
 */
export function getScenicDetail(scenicId) {
  return request({
    url: `/scenics/${scenicId}`,
    method: 'get'
  })
}

/**
 * 根据数据库ID获取景区详情
 * @param {number} id 景区数据库ID
 */
export function getScenicInfo(id) {
  return request({
    url: `/scenics/detail/${id}`,
    method: 'get'
  })
}

/**
 * 创建景区
 * @param {Object} data 景区数据
 */
export function createScenic(data) {
  return request({
    url: '/scenics',
    method: 'post',
    data
  })
}

/**
 * 更新景区
 * @param {number} id 景区ID
 * @param {Object} data 景区数据
 */
export function updateScenic(id, data) {
  return request({
    url: `/scenics/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除景区
 * @param {number} id 景区ID
 */
export function deleteScenic(id) {
  return request({
    url: `/scenics/${id}`,
    method: 'delete'
  })
}

/**
 * 根据城市ID获取景区列表
 * @param {number} cityId 城市ID
 */
export function getScenicsByCityId(cityId) {
  return request({
    url: `/scenics/city/${cityId}`,
    method: 'get'
  })
}
