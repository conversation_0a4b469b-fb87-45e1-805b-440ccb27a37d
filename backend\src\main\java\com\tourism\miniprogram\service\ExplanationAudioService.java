package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.ExplanationAudio;

import java.util.List;

/**
 * 讲解音频服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
public interface ExplanationAudioService extends IService<ExplanationAudio> {

    /**
     * 获取所有启用的音频列表
     *
     * @return 音频列表
     */
    List<ExplanationAudio> getEnabledAudios();
}
