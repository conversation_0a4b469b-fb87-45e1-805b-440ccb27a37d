package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.AreaPointRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区域讲解点关系Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Mapper
public interface AreaPointRelationMapper extends BaseMapper<AreaPointRelation> {

    /**
     * 根据区域ID获取讲解点关系列表
     *
     * @param areaId 区域ID
     * @return 讲解点关系列表
     */
    List<AreaPointRelation> selectByAreaId(@Param("areaId") Integer areaId);

    /**
     * 根据讲解点ID获取区域关系列表
     *
     * @param pointId 讲解点ID
     * @return 区域关系列表
     */
    List<AreaPointRelation> selectByPointId(@Param("pointId") Integer pointId);

    /**
     * 批量插入关系
     *
     * @param relations 关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<AreaPointRelation> relations);

    /**
     * 根据区域ID删除所有关系
     *
     * @param areaId 区域ID
     * @return 删除数量
     */
    int deleteByAreaId(@Param("areaId") Integer areaId);

    /**
     * 根据讲解点ID删除所有关系
     *
     * @param pointId 讲解点ID
     * @return 删除数量
     */
    int deleteByPointId(@Param("pointId") Integer pointId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 更新数量
     */
    int updateSortOrder(@Param("relationId") Integer relationId, @Param("sortOrder") Integer sortOrder);
}
