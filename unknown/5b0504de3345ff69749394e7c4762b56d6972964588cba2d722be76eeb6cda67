// 全局样式重置
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  
  &:hover {
    background: #a8a8a8;
  }
}

// 清除浮动
.clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 布局相关
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

// 间距
.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.ml-10 { margin-left: 10px; }
.ml-20 { margin-left: 20px; }
.mr-10 { margin-right: 10px; }
.mr-20 { margin-right: 20px; }

.pt-10 { padding-top: 10px; }
.pt-20 { padding-top: 20px; }
.pb-10 { padding-bottom: 10px; }
.pb-20 { padding-bottom: 20px; }
.pl-10 { padding-left: 10px; }
.pl-20 { padding-left: 20px; }
.pr-10 { padding-right: 10px; }
.pr-20 { padding-right: 20px; }

// 动画
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all 0.5s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

// Element Plus 样式覆盖
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  .el-card__header {
    border-bottom: 1px solid #f0f0f0;
    padding: 18px 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 500;
    }
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.el-pagination {
  .el-pagination__total {
    color: #606266;
  }
}

.el-form {
  .el-form-item__label {
    color: #606266;
    font-weight: 500;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .el-card .el-card__body {
    padding: 15px;
  }
  
  .el-table {
    font-size: 14px;
  }
  
  .el-pagination {
    text-align: center;
    
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
  }
}

// 自定义组件样式
.page-container {
  padding: 20px;
  
  @media (max-width: 768px) {
    padding: 10px;
  }
}

.search-form {
  .el-form-item {
    margin-bottom: 18px;
  }
}

.table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    .table-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }
  }
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
}

// 空状态
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  
  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 14px;
  }
}
