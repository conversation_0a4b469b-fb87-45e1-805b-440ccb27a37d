package com.tourism.miniprogram.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录响应DTO
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@ApiModel(value = "LoginResponse", description = "登录响应")
public class LoginResponse {

    @ApiModelProperty(value = "用户ID")
    private Integer userId;

    @ApiModelProperty(value = "访问令牌")
    private String token;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "头像URL")
    private String avatarUrl;

    @ApiModelProperty(value = "用户地区")
    private String region;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "是否新用户")
    private Boolean isNewUser;
}
