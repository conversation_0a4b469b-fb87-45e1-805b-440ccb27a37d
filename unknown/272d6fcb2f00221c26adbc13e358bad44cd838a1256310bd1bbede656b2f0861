# 旅游讲解小程序管理后台

## 项目简介

这是一个基于Vue 3 + Element Plus的现代化管理后台，用于管理旅游讲解小程序的用户数据。

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 下一代前端构建工具
- **Element Plus** - 基于Vue 3的组件库
- **Vue Router** - Vue.js官方路由管理器
- **Pinia** - Vue的状态管理库
- **Axios** - HTTP客户端
- **Sass** - CSS预处理器
- **ESLint** - 代码质量检查工具

## 功能特性

- ✅ 现代化的管理后台界面设计
- ✅ 响应式设计，支持移动端和桌面端
- ✅ 用户列表展示（支持分页、搜索、筛选）
- ✅ 用户详情查看
- ✅ 用户信息编辑
- ✅ 微信登录状态展示
- ✅ 侧边栏导航菜单
- ✅ 顶部导航栏和面包屑导航
- ✅ JWT认证处理
- ✅ 错误处理和用户友好的提示信息
- ✅ 加载状态和空数据状态处理

## 项目结构

```
fronted/
├── public/                     # 静态资源
├── src/
│   ├── api/                    # API接口
│   │   └── user.js             # 用户相关接口
│   ├── components/             # 公共组件
│   ├── layout/                 # 布局组件
│   │   ├── components/         # 布局子组件
│   │   │   ├── Sidebar/        # 侧边栏
│   │   │   ├── Navbar.vue      # 顶部导航
│   │   │   ├── Breadcrumb.vue  # 面包屑
│   │   │   └── AppMain.vue     # 主内容区
│   │   └── index.vue           # 布局主文件
│   ├── router/                 # 路由配置
│   │   └── index.js            # 路由主文件
│   ├── stores/                 # 状态管理
│   │   ├── app.js              # 应用状态
│   │   └── user.js             # 用户状态
│   ├── style/                  # 样式文件
│   │   └── index.scss          # 全局样式
│   ├── utils/                  # 工具函数
│   │   ├── index.js            # 通用工具
│   │   ├── request.js          # HTTP请求封装
│   │   └── validate.js         # 验证函数
│   ├── views/                  # 页面组件
│   │   ├── dashboard/          # 仪表盘
│   │   ├── user/               # 用户管理
│   │   │   ├── list.vue        # 用户列表
│   │   │   ├── detail.vue      # 用户详情
│   │   │   └── edit.vue        # 用户编辑
│   │   ├── login/              # 登录页面
│   │   └── error/              # 错误页面
│   ├── App.vue                 # 根组件
│   └── main.js                 # 入口文件
├── .env                        # 环境变量
├── .env.development            # 开发环境变量
├── .env.production             # 生产环境变量
├── index.html                  # HTML模板
├── package.json                # 项目配置
├── vite.config.js              # Vite配置
└── README.md                   # 项目说明
```

## 快速开始

### 1. 安装依赖

```bash
cd fronted
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 构建生产版本

```bash
npm run build
```

### 4. 预览生产版本

```bash
npm run preview
```

## 环境配置

### 开发环境

- 前端服务：http://localhost:3000
- 后端API：http://localhost:8080/api
- 自动代理后端请求

### 生产环境

- 需要配置正确的API地址
- 建议使用Nginx进行反向代理

## 页面说明

### 1. 登录页面 (`/login`)

- 管理员登录界面
- 默认用户名：admin，密码：123456
- 支持测试后端连接功能
- 微信小程序用户说明

### 2. 仪表盘 (`/dashboard`)

- 用户统计数据展示
- 快捷操作入口
- 最近注册用户列表
- 系统状态监控

### 3. 用户管理

#### 用户列表 (`/user/list`)

- 分页展示用户数据
- 支持按昵称、地区、手机号搜索
- 用户头像、基本信息展示
- 微信绑定状态显示
- 查看和编辑操作

#### 用户详情 (`/user/detail/:id`)

- 完整的用户信息展示
- 用户头像和基本资料
- 微信OpenID信息
- 操作记录时间线
- 编辑用户入口

#### 用户编辑 (`/user/edit/:id`)

- 用户信息编辑表单
- 表单验证和错误提示
- 头像预览功能
- 保存和重置操作

## API接口

### 用户相关接口

```javascript
// 获取用户列表
GET /api/user/list?current=1&size=10&nickname=&region=&phone=

// 获取用户详情
GET /api/user/{userId}

// 更新用户信息
PUT /api/user/{userId}

// 微信登录
POST /api/auth/wechat/login

// 测试接口
GET /api/auth/test
```

## 响应式设计

- **桌面端**：完整的侧边栏和多列布局
- **平板端**：自适应布局调整
- **移动端**：折叠侧边栏，单列布局

### 断点设置

- `xs`: < 768px（手机）
- `sm`: ≥ 768px（平板）
- `md`: ≥ 992px（小桌面）
- `lg`: ≥ 1200px（大桌面）

## 开发规范

### 1. 代码风格

- 使用ESLint进行代码检查
- 遵循Vue 3 Composition API规范
- 使用TypeScript类型注释（可选）

### 2. 组件规范

- 组件名使用PascalCase
- 文件名使用kebab-case
- Props定义要包含类型和默认值

### 3. 样式规范

- 使用Sass预处理器
- 遵循BEM命名规范
- 响应式设计优先

## 部署说明

### 1. 构建项目

```bash
npm run build
```

### 2. 部署到服务器

将`dist`目录下的文件部署到Web服务器

### 3. Nginx配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

### 1. 开发环境跨域问题

项目已配置Vite代理，自动转发API请求到后端服务。

### 2. 生产环境API地址配置

修改`.env.production`文件中的`VITE_API_BASE_URL`。

### 3. 路由404问题

确保服务器配置了前端路由的fallback到`index.html`。

## 浏览器支持

- Chrome ≥ 87
- Firefox ≥ 78
- Safari ≥ 14
- Edge ≥ 88

## 开发团队

如有问题，请联系开发团队。

## 更新日志

### v1.0.0 (2023-12-01)

- 初始版本发布
- 完整的用户管理功能
- 响应式设计支持
- 微信小程序用户数据管理
