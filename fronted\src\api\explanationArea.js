import request from '@/utils/request'

/**
 * 分页获取讲解区域列表
 * @param {Object} params 查询参数
 */
export function getExplanationAreaPage(params) {
  return request({
    url: '/explanation-areas/page',
    method: 'get',
    params
  })
}

/**
 * 获取讲解区域详情
 * @param {number} id 区域ID
 */
export function getExplanationAreaById(id) {
  return request({
    url: `/explanation-areas/${id}`,
    method: 'get'
  })
}

/**
 * 根据产品ID获取区域列表
 * @param {number} productId 产品ID
 */
export function getAreasByProductId(productId) {
  return request({
    url: `/explanation-areas/product/${productId}`,
    method: 'get'
  })
}

/**
 * 创建讲解区域
 * @param {Object} data 区域数据
 */
export function createExplanationArea(data) {
  return request({
    url: '/explanation-areas',
    method: 'post',
    data
  })
}

/**
 * 更新讲解区域
 * @param {number} id 区域ID
 * @param {Object} data 区域数据
 */
export function updateExplanationArea(id, data) {
  return request({
    url: `/explanation-areas/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除讲解区域
 * @param {number} id 区域ID
 */
export function deleteExplanationArea(id) {
  return request({
    url: `/explanation-areas/${id}`,
    method: 'delete'
  })
}
