package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 首页轮播图实体类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("shouye_carousel")
@ApiModel(value = "ShouyeCarousel对象", description = "首页轮播图")
public class ShouyeCarousel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "carousel_id", type = IdType.AUTO)
    private Integer carouselId;

    @ApiModelProperty(value = "图片URL")
    private String imageUrl;
}
