package com.tourism.miniprogram.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tourism.miniprogram.entity.GuideProduct;
import com.tourism.miniprogram.mapper.GuideProductMapper;
import com.tourism.miniprogram.service.GuideProductService;
import org.springframework.stereotype.Service;

/**
 * 讲解产品服务实现类
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Service
public class GuideProductServiceImpl extends ServiceImpl<GuideProductMapper, GuideProduct> implements GuideProductService {

}
