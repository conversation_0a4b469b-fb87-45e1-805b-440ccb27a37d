package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.Order;
import com.tourism.miniprogram.service.OrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 订单控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/orders")
@Api(tags = "订单管理")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 分页获取订单列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取订单列表", notes = "分页获取订单列表，支持按订单号、用户ID、状态筛选")
    public Result<IPage<Order>> getOrderPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "订单号") @RequestParam(required = false) String orderNo,
            @ApiParam(value = "用户ID") @RequestParam(required = false) Integer userId,
            @ApiParam(value = "订单状态") @RequestParam(required = false) String status) {
        try {
            Page<Order> page = new Page<>(current, size);
            QueryWrapper<Order> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(orderNo)) {
                queryWrapper.like("order_no", orderNo);
            }
            if (userId != null) {
                queryWrapper.eq("user_id", userId);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("created_at");

            IPage<Order> orderPage = orderService.page(page, queryWrapper);
            return Result.success(orderPage);
        } catch (Exception e) {
            log.error("分页获取订单列表失败", e);
            return Result.error("获取订单列表失败");
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取订单详情", notes = "根据ID获取订单详细信息")
    public Result<Order> getOrderById(@ApiParam(value = "订单ID", required = true) @PathVariable Integer id) {
        try {
            Order order = orderService.getById(id);
            if (order == null) {
                return Result.error(404, "订单不存在");
            }
            return Result.success(order);
        } catch (Exception e) {
            log.error("获取订单详情失败，id: {}", id, e);
            return Result.error("获取订单详情失败");
        }
    }

    /**
     * 根据用户ID获取订单列表
     */
    @GetMapping("/user/{userId}")
    @ApiOperation(value = "根据用户ID获取订单列表", notes = "根据用户ID获取该用户的所有订单")
    public Result<List<Order>> getOrdersByUserId(@ApiParam(value = "用户ID", required = true) @PathVariable Integer userId) {
        try {
            List<Order> orders = orderService.getOrdersByUserId(userId);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("根据用户ID获取订单列表失败，userId: {}", userId, e);
            return Result.error("获取订单列表失败");
        }
    }

    /**
     * 根据状态获取订单列表
     */
    @GetMapping("/status/{status}")
    @ApiOperation(value = "根据状态获取订单列表", notes = "根据状态获取订单列表")
    public Result<List<Order>> getOrdersByStatus(@ApiParam(value = "订单状态", required = true) @PathVariable String status) {
        try {
            List<Order> orders = orderService.getOrdersByStatus(status);
            return Result.success(orders);
        } catch (Exception e) {
            log.error("根据状态获取订单列表失败，status: {}", status, e);
            return Result.error("获取订单列表失败");
        }
    }

    /**
     * 创建订单
     */
    @PostMapping
    @ApiOperation(value = "创建订单", notes = "创建新的订单并生成相应门票")
    public Result<String> createOrder(@RequestBody @Valid Order order) {
        try {
            boolean success = orderService.createOrderWithCoupons(order);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建订单失败", e);
            return Result.error("创建订单失败");
        }
    }

    /**
     * 支付订单
     */
    @PostMapping("/{id}/pay")
    @ApiOperation(value = "支付订单", notes = "支付订单")
    public Result<String> payOrder(@ApiParam(value = "订单ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = orderService.payOrder(id);
            if (success) {
                return Result.success("支付成功");
            } else {
                return Result.error("支付失败");
            }
        } catch (Exception e) {
            log.error("支付订单失败，id: {}", id, e);
            return Result.error("支付订单失败");
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/{id}/cancel")
    @ApiOperation(value = "取消订单", notes = "取消订单")
    public Result<String> cancelOrder(@ApiParam(value = "订单ID", required = true) @PathVariable Integer id) {
        try {
            Order order = orderService.getById(id);
            if (order == null) {
                return Result.error(404, "订单不存在");
            }

            if (!"pending".equals(order.getStatus())) {
                return Result.error("只能取消待付款的订单");
            }

            order.setStatus("canceled");
            boolean success = orderService.updateById(order);
            if (success) {
                return Result.success("取消成功");
            } else {
                return Result.error("取消失败");
            }
        } catch (Exception e) {
            log.error("取消订单失败，id: {}", id, e);
            return Result.error("取消订单失败");
        }
    }

    /**
     * 删除订单
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除订单", notes = "删除订单")
    public Result<String> deleteOrder(@ApiParam(value = "订单ID", required = true) @PathVariable Integer id) {
        try {
            Order order = orderService.getById(id);
            if (order == null) {
                return Result.error(404, "订单不存在");
            }

            boolean success = orderService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除订单失败，id: {}", id, e);
            return Result.error("删除订单失败");
        }
    }
}
