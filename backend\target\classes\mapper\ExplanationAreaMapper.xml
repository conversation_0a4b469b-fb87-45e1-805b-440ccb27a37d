<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationAreaMapper">

    <!-- 根据产品ID获取区域列表 -->
    <select id="selectAreasByProductId" resultType="com.tourism.miniprogram.entity.ExplanationArea">
        SELECT area_id, product_id, area_name, sort_order, created_at, updated_at
        FROM explanation_area
        WHERE product_id = #{productId}
        ORDER BY sort_order ASC, area_id DESC
    </select>

</mapper>
