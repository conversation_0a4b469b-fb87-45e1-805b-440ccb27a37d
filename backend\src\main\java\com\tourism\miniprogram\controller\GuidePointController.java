package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.GuidePoint;
import com.tourism.miniprogram.service.GuidePointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 讲解点控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/guide-points")
@Api(tags = "讲解点管理")
public class GuidePointController {

    @Autowired
    private GuidePointService guidePointService;

    /**
     * 分页获取讲解点列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲解点列表", notes = "分页获取讲解点列表，支持按标题、产品ID、位置筛选")
    public Result<IPage<GuidePoint>> getGuidePointPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "讲解点名称") @RequestParam(required = false) String pointName,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<GuidePoint> page = new Page<>(current, size);
            QueryWrapper<GuidePoint> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(pointName)) {
                queryWrapper.like("point_name", pointName);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByAsc("sort_order").orderByDesc("point_id");

            IPage<GuidePoint> guidePointPage = guidePointService.page(page, queryWrapper);
            return Result.success(guidePointPage);
        } catch (Exception e) {
            log.error("分页获取讲解点列表失败", e);
            return Result.error("获取讲解点列表失败");
        }
    }

    /**
     * 获取讲解点详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲解点详情", notes = "根据ID获取讲解点详细信息")
    public Result<GuidePoint> getGuidePointById(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id) {
        try {
            GuidePoint guidePoint = guidePointService.getById(id);
            if (guidePoint == null) {
                return Result.error(404, "讲解点不存在");
            }
            return Result.success(guidePoint);
        } catch (Exception e) {
            log.error("获取讲解点详情失败，id: {}", id, e);
            return Result.error("获取讲解点详情失败");
        }
    }

    /**
     * 创建讲解点
     */
    @PostMapping
    @ApiOperation(value = "创建讲解点", notes = "创建新的讲解点")
    public Result<String> createGuidePoint(@RequestBody @Valid GuidePoint guidePoint) {
        try {
            boolean success = guidePointService.save(guidePoint);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲解点失败", e);
            return Result.error("创建讲解点失败");
        }
    }

    /**
     * 更新讲解点
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲解点", notes = "更新讲解点信息")
    public Result<String> updateGuidePoint(
            @ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid GuidePoint guidePoint) {
        try {
            GuidePoint existGuidePoint = guidePointService.getById(id);
            if (existGuidePoint == null) {
                return Result.error(404, "讲解点不存在");
            }

            guidePoint.setPointId(id);
            boolean success = guidePointService.updateById(guidePoint);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲解点失败，id: {}", id, e);
            return Result.error("更新讲解点失败");
        }
    }

    /**
     * 删除讲解点
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲解点", notes = "删除讲解点")
    public Result<String> deleteGuidePoint(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id) {
        try {
            GuidePoint guidePoint = guidePointService.getById(id);
            if (guidePoint == null) {
                return Result.error(404, "讲解点不存在");
            }

            boolean success = guidePointService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲解点失败，id: {}", id, e);
            return Result.error("删除讲解点失败");
        }
    }
}
