package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.PointAudioRelation;

import java.util.List;

/**
 * 讲解点音频关系服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
public interface PointAudioRelationService extends IService<PointAudioRelation> {

    /**
     * 根据讲解点ID获取音频关系列表
     *
     * @param pointId 讲解点ID
     * @return 音频关系列表
     */
    List<PointAudioRelation> getByPointId(Integer pointId);

    /**
     * 根据音频ID获取讲解点关系列表
     *
     * @param audioId 音频ID
     * @return 讲解点关系列表
     */
    List<PointAudioRelation> getByAudioId(Integer audioId);

    /**
     * 添加讲解点音频关系
     *
     * @param pointId 讲解点ID
     * @param audioId 音频ID
     * @param sortOrder 排序
     * @return 是否成功
     */
    boolean addRelation(Integer pointId, Integer audioId, Integer sortOrder);

    /**
     * 删除讲解点音频关系
     *
     * @param pointId 讲解点ID
     * @param audioId 音频ID
     * @return 是否成功
     */
    boolean removeRelation(Integer pointId, Integer audioId);

    /**
     * 批量添加关系
     *
     * @param relations 关系列表
     * @return 是否成功
     */
    boolean batchAddRelations(List<PointAudioRelation> relations);

    /**
     * 根据讲解点ID删除所有关系
     *
     * @param pointId 讲解点ID
     * @return 是否成功
     */
    boolean removeByPointId(Integer pointId);

    /**
     * 根据音频ID删除所有关系
     *
     * @param audioId 音频ID
     * @return 是否成功
     */
    boolean removeByAudioId(Integer audioId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 是否成功
     */
    boolean updateSortOrder(Integer relationId, Integer sortOrder);

    /**
     * 批量更新排序
     *
     * @param relationIds 关系ID列表
     * @param sortOrders 排序值列表
     * @return 是否成功
     */
    boolean batchUpdateSortOrder(List<Integer> relationIds, List<Integer> sortOrders);
}
