import request from '@/utils/request'

// 分页获取组合包列表
export function getProductBundlePage(params) {
  return request({
    url: '/product-bundles/page',
    method: 'get',
    params
  })
}

// 获取组合包列表
export function getProductBundleList() {
  return request({
    url: '/product-bundles',
    method: 'get'
  })
}

// 获取组合包详情
export function getProductBundleById(id) {
  return request({
    url: `/product-bundles/${id}`,
    method: 'get'
  })
}

// 创建组合包
export function createProductBundle(data) {
  return request({
    url: '/product-bundles',
    method: 'post',
    data
  })
}

// 更新组合包
export function updateProductBundle(id, data) {
  return request({
    url: `/product-bundles/${id}`,
    method: 'put',
    data
  })
}

// 删除组合包
export function deleteProductBundle(id) {
  return request({
    url: `/product-bundles/${id}`,
    method: 'delete'
  })
}
