-- 关系管理系统测试数据
-- 用于演示和测试多对多关系功能

USE tourism_miniprogram;

-- 清理现有测试数据
DELETE FROM point_audio_relation WHERE relation_id > 0;
DELETE FROM area_point_relation WHERE relation_id > 0;
DELETE FROM product_area_relation WHERE relation_id > 0;

-- 插入更多测试区域数据
INSERT INTO explanation_area (area_name, area_description, area_image, sort_order, status) VALUES
('山门广场', '景区主入口，设有游客服务中心和停车场', '/images/areas/entrance.jpg', 1, 1),
('古建筑群', '明清时期古建筑群落，包含大殿、厢房等', '/images/areas/ancient.jpg', 2, 1),
('园林景观', '精美的古典园林，亭台楼阁错落有致', '/images/areas/garden.jpg', 3, 1),
('文物展厅', '珍贵文物展示区域，展示历史文化', '/images/areas/museum.jpg', 4, 1),
('休闲茶室', '传统茶文化体验区，可品茶休憩', '/images/areas/teahouse.jpg', 5, 1),
('观景台', '制高点观景平台，可俯瞰全景', '/images/areas/viewpoint.jpg', 6, 1);

-- 插入更多测试音频数据
INSERT INTO explanation_audio (audio_name, audio_description, audio_url, audio_image, duration, sort_order, status) VALUES
('景区概览', '景区整体介绍和游览路线指引', '/audio/overview.mp3', '/images/audio/overview.jpg', 180, 1, 1),
('历史沿革', '景区从古至今的历史发展脉络', '/audio/history_detail.mp3', '/images/audio/history.jpg', 240, 2, 1),
('建筑艺术', '古建筑的艺术特色和建造工艺', '/audio/architecture_art.mp3', '/images/audio/architecture.jpg', 200, 3, 1),
('园林美学', '中国古典园林的设计理念和美学价值', '/audio/garden_beauty.mp3', '/images/audio/garden.jpg', 220, 4, 1),
('文物珍品', '重要文物的历史价值和文化内涵', '/audio/treasures.mp3', '/images/audio/treasures.jpg', 300, 5, 1),
('民俗文化', '当地传统民俗和文化习俗介绍', '/audio/folklore.mp3', '/images/audio/folklore.jpg', 180, 6, 1),
('传说故事', '与景区相关的历史传说和民间故事', '/audio/legends.mp3', '/images/audio/legends.jpg', 160, 7, 1),
('季节变化', '景区四季不同的景观特色', '/audio/seasons.mp3', '/images/audio/seasons.jpg', 140, 8, 1),
('摄影指南', '最佳拍摄点位和摄影技巧分享', '/audio/photography.mp3', '/images/audio/photo.jpg', 120, 9, 1),
('安全提示', '游览安全注意事项和应急信息', '/audio/safety.mp3', '/images/audio/safety.jpg', 100, 10, 1);

-- 创建产品-区域关系示例数据
-- 假设产品ID 1-5 已存在
INSERT INTO product_area_relation (product_id, area_id, sort_order) VALUES
-- 产品1：经典游览路线
(1, 1, 1), -- 山门广场
(1, 2, 2), -- 古建筑群
(1, 3, 3), -- 园林景观
(1, 4, 4), -- 文物展厅

-- 产品2：深度文化体验
(2, 2, 1), -- 古建筑群
(2, 4, 2), -- 文物展厅
(2, 5, 3), -- 休闲茶室

-- 产品3：全景观光游
(3, 1, 1), -- 山门广场
(3, 3, 2), -- 园林景观
(3, 6, 3), -- 观景台

-- 产品4：历史文化专线
(4, 2, 1), -- 古建筑群
(4, 4, 2), -- 文物展厅

-- 产品5：休闲度假游
(5, 3, 1), -- 园林景观
(5, 5, 2), -- 休闲茶室
(5, 6, 3); -- 观景台

-- 创建区域-讲解点关系示例数据
-- 假设讲解点ID 1-15 已存在
INSERT INTO area_point_relation (area_id, point_id, sort_order) VALUES
-- 山门广场区域
(1, 1, 1),  -- 景区大门
(1, 2, 2),  -- 游客中心
(1, 3, 3),  -- 导览图

-- 古建筑群区域
(2, 4, 1),  -- 大雄宝殿
(2, 5, 2),  -- 天王殿
(2, 6, 3),  -- 钟楼
(2, 7, 4),  -- 鼓楼

-- 园林景观区域
(3, 8, 1),  -- 荷花池
(3, 9, 2),  -- 假山石
(3, 10, 3), -- 古亭
(3, 11, 4), -- 曲桥

-- 文物展厅区域
(4, 12, 1), -- 青铜器展区
(4, 13, 2), -- 书画展区
(4, 14, 3), -- 瓷器展区

-- 休闲茶室区域
(5, 15, 1), -- 品茶区

-- 观景台区域
(6, 16, 1); -- 观景平台

-- 创建讲解点-音频关系示例数据
INSERT INTO point_audio_relation (point_id, audio_id, sort_order) VALUES
-- 景区大门
(1, 1, 1),  -- 景区概览
(1, 8, 2),  -- 季节变化

-- 游客中心
(2, 1, 1),  -- 景区概览
(2, 10, 2), -- 安全提示

-- 大雄宝殿
(4, 2, 1),  -- 历史沿革
(4, 3, 2),  -- 建筑艺术
(4, 7, 3),  -- 传说故事

-- 天王殿
(5, 3, 1),  -- 建筑艺术
(5, 6, 2),  -- 民俗文化

-- 荷花池
(8, 4, 1),  -- 园林美学
(8, 8, 2),  -- 季节变化
(8, 9, 3),  -- 摄影指南

-- 假山石
(9, 4, 1),  -- 园林美学
(9, 9, 2),  -- 摄影指南

-- 青铜器展区
(12, 5, 1), -- 文物珍品
(12, 2, 2), -- 历史沿革

-- 书画展区
(13, 5, 1), -- 文物珍品
(13, 6, 2), -- 民俗文化

-- 品茶区
(15, 6, 1), -- 民俗文化
(15, 7, 2), -- 传说故事

-- 观景平台
(16, 1, 1), -- 景区概览
(16, 9, 2), -- 摄影指南
(16, 8, 3); -- 季节变化

-- 验证数据插入结果
SELECT '=== 数据插入完成 ===' AS status;

SELECT 
    '产品-区域关系' AS relation_type,
    COUNT(*) AS count
FROM product_area_relation

UNION ALL

SELECT 
    '区域-讲解点关系' AS relation_type,
    COUNT(*) AS count
FROM area_point_relation

UNION ALL

SELECT 
    '讲解点-音频关系' AS relation_type,
    COUNT(*) AS count
FROM point_audio_relation;

-- 显示关系统计
SELECT 
    ea.area_name,
    COUNT(par.relation_id) AS product_count,
    COUNT(apr.relation_id) AS point_count
FROM explanation_area ea
LEFT JOIN product_area_relation par ON ea.area_id = par.area_id
LEFT JOIN area_point_relation apr ON ea.area_id = apr.area_id
GROUP BY ea.area_id, ea.area_name
ORDER BY ea.sort_order;

SELECT '测试数据初始化完成！可以开始测试关系管理功能。' AS message;
