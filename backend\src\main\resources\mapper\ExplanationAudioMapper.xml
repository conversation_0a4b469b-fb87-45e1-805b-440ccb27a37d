<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationAudioMapper">

    <!-- 根据讲解点ID获取音频列表 -->
    <select id="selectAudiosByPointId" resultType="com.tourism.miniprogram.entity.ExplanationAudio">
        SELECT audio_id, point_id, audio_name, audio_image, duration, audio_url, sort_order, created_at, updated_at
        FROM explanation_audio
        WHERE point_id = #{pointId}
        ORDER BY sort_order ASC, audio_id DESC
    </select>

</mapper>
