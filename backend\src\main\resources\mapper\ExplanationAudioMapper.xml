<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ExplanationAudioMapper">

    <!-- 根据讲解点ID获取音频列表 -->
    <select id="selectAudiosByPointId" resultType="com.tourism.miniprogram.entity.ExplanationAudio">
        SELECT ea.audio_id, ea.audio_name, ea.audio_description, ea.audio_url, ea.audio_image, ea.duration, ea.sort_order, ea.status, ea.created_at, ea.updated_at
        FROM explanation_audio ea
        INNER JOIN point_audio_relation par ON ea.audio_id = par.audio_id
        WHERE par.point_id = #{pointId}
        ORDER BY par.sort_order ASC, ea.audio_id DESC
    </select>

</mapper>
