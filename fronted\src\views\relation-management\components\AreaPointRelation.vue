<template>
  <div class="relation-container">
    <!-- 操作栏 -->
    <div class="operation-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-select
            v-model="selectedAreaId"
            placeholder="选择区域"
            filterable
            clearable
            style="width: 100%"
            @change="handleAreaChange"
          >
            <el-option
              v-for="area in areaList"
              :key="area.areaId"
              :label="area.areaName"
              :value="area.areaId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select
            v-model="selectedPointId"
            placeholder="选择讲解点"
            filterable
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="point in pointList"
              :key="point.pointId"
              :label="point.title"
              :value="point.pointId"
            />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleAddRelation" :disabled="!selectedAreaId || !selectedPointId">
            添加关系
          </el-button>
          <el-button @click="handleRefresh">刷新</el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 关系列表 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>区域-讲解点关系列表</span>
          <span class="count-badge">共 {{ relationList.length }} 条关系</span>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="relationList"
        style="width: 100%"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="relationId" label="关系ID" width="100" />
        <el-table-column prop="areaId" label="区域ID" width="100" />
        <el-table-column label="区域名称" min-width="150">
          <template #default="{ row }">
            {{ getAreaName(row.areaId) }}
          </template>
        </el-table-column>
        <el-table-column prop="pointId" label="讲解点ID" width="120" />
        <el-table-column label="讲解点名称" min-width="200">
          <template #default="{ row }">
            {{ getPointName(row.pointId) }}
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="120" sortable="custom">
          <template #default="{ row }">
            <el-input-number
              v-model="row.sortOrder"
              :min="0"
              :max="999"
              size="small"
              @change="handleSortOrderChange(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              type="danger"
              size="small"
              @click="handleDeleteRelation(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPointsByAreaId,
  addAreaPointRelation,
  removeAreaPointRelation,
  updateAreaPointSortOrder,
  getAllAreas
} from '@/api/relations'
import { getGuidePointPage } from '@/api/guidePoint'

// 响应式数据
const loading = ref(false)
const relationList = ref([])
const areaList = ref([])
const pointList = ref([])
const selectedAreaId = ref('')
const selectedPointId = ref('')

// 获取区域列表
const fetchAreaList = async () => {
  try {
    const response = await getAllAreas()
    if (response.code === 200) {
      areaList.value = response.data || []
    }
  } catch (error) {
    console.error('获取区域列表失败:', error)
    ElMessage.error('获取区域列表失败')
  }
}

// 获取讲解点列表
const fetchPointList = async () => {
  try {
    const response = await getGuidePointPage({ current: 1, size: 1000 })
    if (response.code === 200) {
      pointList.value = response.data.records || []
    }
  } catch (error) {
    console.error('获取讲解点列表失败:', error)
    ElMessage.error('获取讲解点列表失败')
  }
}

// 获取关系列表
const fetchRelationList = async () => {
  if (!selectedAreaId.value) {
    relationList.value = []
    return
  }

  try {
    loading.value = true
    const response = await getPointsByAreaId(selectedAreaId.value)
    if (response.code === 200) {
      relationList.value = response.data || []
    }
  } catch (error) {
    console.error('获取关系列表失败:', error)
    ElMessage.error('获取关系列表失败')
  } finally {
    loading.value = false
  }
}

// 区域选择变化
const handleAreaChange = () => {
  fetchRelationList()
}

// 添加关系
const handleAddRelation = async () => {
  try {
    const data = {
      areaId: selectedAreaId.value,
      pointId: selectedPointId.value,
      sortOrder: 0
    }
    
    const response = await addAreaPointRelation(data)
    if (response.code === 200) {
      ElMessage.success('添加关系成功')
      selectedPointId.value = ''
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '添加关系失败')
    }
  } catch (error) {
    console.error('添加关系失败:', error)
    ElMessage.error('添加关系失败')
  }
}

// 删除关系
const handleDeleteRelation = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个关系吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await removeAreaPointRelation(row.areaId, row.pointId)
    if (response.code === 200) {
      ElMessage.success('删除关系成功')
      fetchRelationList()
    } else {
      ElMessage.error(response.message || '删除关系失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除关系失败:', error)
      ElMessage.error('删除关系失败')
    }
  }
}

// 排序变化
const handleSortOrderChange = async (row) => {
  try {
    const response = await updateAreaPointSortOrder(row.relationId, row.sortOrder)
    if (response.code === 200) {
      ElMessage.success('更新排序成功')
    } else {
      ElMessage.error(response.message || '更新排序失败')
    }
  } catch (error) {
    console.error('更新排序失败:', error)
    ElMessage.error('更新排序失败')
  }
}

// 表格排序
const handleSortChange = ({ prop, order }) => {
  if (prop === 'sortOrder') {
    if (order === 'ascending') {
      relationList.value.sort((a, b) => a.sortOrder - b.sortOrder)
    } else if (order === 'descending') {
      relationList.value.sort((a, b) => b.sortOrder - a.sortOrder)
    }
  }
}

// 刷新
const handleRefresh = () => {
  fetchRelationList()
}

// 获取区域名称
const getAreaName = (areaId) => {
  const area = areaList.value.find(a => a.areaId === areaId)
  return area ? area.areaName : `区域${areaId}`
}

// 获取讲解点名称
const getPointName = (pointId) => {
  const point = pointList.value.find(p => p.pointId === pointId)
  return point ? point.title : `讲解点${pointId}`
}

// 初始化
onMounted(() => {
  fetchAreaList()
  fetchPointList()
})
</script>

<style lang="scss" scoped>
.relation-container {
  .operation-bar {
    margin-bottom: 20px;
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .count-badge {
        background: #f0f2f5;
        color: #606266;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
      }
    }
  }
}
</style>
