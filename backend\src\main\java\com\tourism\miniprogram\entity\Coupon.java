package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数字门票实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("coupons")
@ApiModel(value = "Coupon对象", description = "数字门票信息")
public class Coupon implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门票ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "卡券唯一编码")
    @TableField("code")
    @NotBlank(message = "卡券编码不能为空")
    private String code;

    @ApiModelProperty(value = "关联产品ID")
    @TableField("product_id")
    private Integer productId;

    @ApiModelProperty(value = "关联组合包ID")
    @TableField("bundle_id")
    private Integer bundleId;

    @ApiModelProperty(value = "来源订单ID")
    @TableField("order_id")
    @NotNull(message = "订单ID不能为空")
    private Integer orderId;

    @ApiModelProperty(value = "归属用户ID")
    @TableField("user_id")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    @ApiModelProperty(value = "关联景区ID")
    @TableField("scenic_id")
    @NotNull(message = "景区ID不能为空")
    private Integer scenicId;

    @ApiModelProperty(value = "状态：unactivated-未激活，active-已激活，used-已使用，expired-已过期")
    @TableField("status")
    @NotBlank(message = "状态不能为空")
    private String status;

    @ApiModelProperty(value = "绑定的激活码ID")
    @TableField("activation_code_id")
    private Integer activationCodeId;

    @ApiModelProperty(value = "生效时间")
    @TableField("valid_from")
    private LocalDateTime validFrom;

    @ApiModelProperty(value = "过期时间")
    @TableField("valid_to")
    private LocalDateTime validTo;

    @ApiModelProperty(value = "使用时间")
    @TableField("used_at")
    private LocalDateTime usedAt;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
