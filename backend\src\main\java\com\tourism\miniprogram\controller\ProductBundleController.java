package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ProductBundle;
import com.tourism.miniprogram.service.ProductBundleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品组合包控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/product-bundles")
@Api(tags = "产品组合包管理")
public class ProductBundleController {

    @Autowired
    private ProductBundleService productBundleService;

    /**
     * 分页获取组合包列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取组合包列表", notes = "分页获取组合包列表，支持按名称筛选")
    public Result<IPage<ProductBundle>> getProductBundlePage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "组合包名称") @RequestParam(required = false) String name,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<ProductBundle> page = new Page<>(current, size);
            QueryWrapper<ProductBundle> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(name)) {
                queryWrapper.like("name", name);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByDesc("id");

            IPage<ProductBundle> bundlePage = productBundleService.page(page, queryWrapper);
            return Result.success(bundlePage);
        } catch (Exception e) {
            log.error("分页获取组合包列表失败", e);
            return Result.error("获取组合包列表失败");
        }
    }

    /**
     * 获取组合包列表
     */
    @GetMapping
    @ApiOperation(value = "获取组合包列表", notes = "获取所有启用的组合包列表")
    public Result<List<ProductBundle>> getProductBundles() {
        try {
            QueryWrapper<ProductBundle> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", 1);
            queryWrapper.orderByDesc("id");
            List<ProductBundle> bundles = productBundleService.list(queryWrapper);
            return Result.success(bundles);
        } catch (Exception e) {
            log.error("获取组合包列表失败", e);
            return Result.error("获取组合包列表失败");
        }
    }

    /**
     * 获取组合包详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取组合包详情", notes = "根据ID获取组合包详情")
    public Result<ProductBundle> getProductBundleById(@ApiParam(value = "组合包ID", required = true) @PathVariable Integer id) {
        try {
            ProductBundle bundle = productBundleService.getById(id);
            if (bundle == null) {
                return Result.error(404, "组合包不存在");
            }
            return Result.success(bundle);
        } catch (Exception e) {
            log.error("获取组合包详情失败，id: {}", id, e);
            return Result.error("获取组合包详情失败");
        }
    }

    /**
     * 创建组合包
     */
    @PostMapping
    @ApiOperation(value = "创建组合包", notes = "创建新的组合包")
    public Result<String> createProductBundle(@RequestBody @Valid ProductBundle bundle) {
        try {
            boolean success = productBundleService.save(bundle);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建组合包失败", e);
            return Result.error("创建组合包失败");
        }
    }

    /**
     * 更新组合包
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新组合包", notes = "根据ID更新组合包信息")
    public Result<String> updateProductBundle(
            @ApiParam(value = "组合包ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid ProductBundle bundle) {
        try {
            bundle.setId(id);
            boolean success = productBundleService.updateById(bundle);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新组合包失败，id: {}", id, e);
            return Result.error("更新组合包失败");
        }
    }

    /**
     * 删除组合包
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除组合包", notes = "根据ID删除组合包")
    public Result<String> deleteProductBundle(@ApiParam(value = "组合包ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = productBundleService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除组合包失败，id: {}", id, e);
            return Result.error("删除组合包失败");
        }
    }
}
