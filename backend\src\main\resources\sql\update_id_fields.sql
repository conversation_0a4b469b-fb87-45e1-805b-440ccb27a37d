-- 更新ID字段类型为VARCHAR以支持字符串格式的ID
-- 修改日期：2025-01-06

USE `tourism_miniprogram`;

-- 修改 product_table 表的 scenic_id 字段类型
ALTER TABLE product_table MODIFY COLUMN scenic_id VARCHAR(255) NOT NULL COMMENT '景区ID';

-- 修改 point_table 表的 product_id 字段类型  
ALTER TABLE point_table MODIFY COLUMN product_id VARCHAR(255) NOT NULL COMMENT '产品ID';

-- 如果有其他表也使用了这些ID字段，也需要相应修改
-- 例如：orders 表、coupons 表等

-- 检查是否有外键约束需要处理
-- 如果有外键约束，需要先删除约束，修改字段类型，然后重新添加约束

-- 示例：如果有外键约束
-- ALTER TABLE point_table DROP FOREIGN KEY fk_point_product;
-- ALTER TABLE point_table MODIFY COLUMN product_id VARCHAR(255) NOT NULL;
-- ALTER TABLE point_table ADD CONSTRAINT fk_point_product FOREIGN KEY (product_id) REFERENCES product_table(product_id);

-- 更新说明：
-- 1. scenic_id 从 INT 改为 VARCHAR(255)，支持 "scenic_1749304543166_xsxcmn" 格式
-- 2. product_id 从 INT 改为 VARCHAR(255)，保持一致性
-- 3. 如果现有数据是数字格式，MySQL会自动转换为字符串格式
-- 4. 建议在执行前备份数据库
