package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Coupon;

import java.util.List;

/**
 * 数字门票服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface CouponService extends IService<Coupon> {

    /**
     * 根据用户ID获取门票列表
     *
     * @param userId 用户ID
     * @return 门票列表
     */
    List<Coupon> getCouponsByUserId(Integer userId);

    /**
     * 根据景区ID获取门票列表
     *
     * @param scenicId 景区ID
     * @return 门票列表
     */
    List<Coupon> getCouponsByScenicId(Integer scenicId);

    /**
     * 根据状态获取门票列表
     *
     * @param status 门票状态
     * @return 门票列表
     */
    List<Coupon> getCouponsByStatus(String status);

    /**
     * 根据用户ID和景区ID获取有效门票
     *
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 门票列表
     */
    List<Coupon> getValidCouponsByUserAndScenic(Integer userId, Integer scenicId);

    /**
     * 激活门票
     *
     * @param couponId 门票ID
     * @param activationCode 激活码
     * @return 是否成功
     */
    boolean activateCoupon(Integer couponId, String activationCode);

    /**
     * 使用门票
     *
     * @param couponId 门票ID
     * @param userId 用户ID
     * @param scenicId 景区ID
     * @return 是否成功
     */
    boolean useCoupon(Integer couponId, Integer userId, Integer scenicId);

    /**
     * 生成门票码
     *
     * @return 门票码
     */
    String generateCouponCode();
}
