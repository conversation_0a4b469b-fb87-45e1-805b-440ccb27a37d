package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ExplanationPoint;
import com.tourism.miniprogram.service.ExplanationPointService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 讲解点控制器
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
@Slf4j
@RestController
@RequestMapping("/explanation-points")
@Api(tags = "讲解点管理")
public class ExplanationPointController {

    @Autowired
    private ExplanationPointService explanationPointService;

    /**
     * 分页获取讲解点列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取讲解点列表", notes = "分页获取讲解点列表，支持按讲解点名称、区域ID筛选")
    public Result<IPage<ExplanationPoint>> getExplanationPointPage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "讲解点名称") @RequestParam(required = false) String pointName,
            @ApiParam(value = "状态") @RequestParam(required = false) Integer status) {
        try {
            Page<ExplanationPoint> page = new Page<>(current, size);
            QueryWrapper<ExplanationPoint> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(pointName)) {
                queryWrapper.like("point_name", pointName);
            }
            if (status != null) {
                queryWrapper.eq("status", status);
            }
            queryWrapper.orderByAsc("sort_order").orderByDesc("point_id");

            IPage<ExplanationPoint> pointPage = explanationPointService.page(page, queryWrapper);
            return Result.success(pointPage);
        } catch (Exception e) {
            log.error("分页获取讲解点列表失败", e);
            return Result.error("获取讲解点列表失败");
        }
    }

    /**
     * 获取讲解点详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取讲解点详情", notes = "根据ID获取讲解点详细信息")
    public Result<ExplanationPoint> getExplanationPointById(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationPoint point = explanationPointService.getById(id);
            if (point == null) {
                return Result.error(404, "讲解点不存在");
            }
            return Result.success(point);
        } catch (Exception e) {
            log.error("获取讲解点详情失败，id: {}", id, e);
            return Result.error("获取讲解点详情失败");
        }
    }

    /**
     * 根据区域ID获取讲解点列表
     */
    @GetMapping("/area/{areaId}")
    @ApiOperation(value = "根据区域ID获取讲解点列表", notes = "根据区域ID获取该区域的所有讲解点")
    public Result<List<ExplanationPoint>> getPointsByAreaId(@ApiParam(value = "区域ID", required = true) @PathVariable Integer areaId) {
        try {
            List<ExplanationPoint> points = explanationPointService.getPointsByAreaId(areaId);
            return Result.success(points);
        } catch (Exception e) {
            log.error("根据区域ID获取讲解点列表失败，areaId: {}", areaId, e);
            return Result.error("获取讲解点列表失败");
        }
    }

    /**
     * 创建讲解点
     */
    @PostMapping
    @ApiOperation(value = "创建讲解点", notes = "创建新的讲解点")
    public Result<String> createExplanationPoint(@RequestBody @Valid ExplanationPoint point) {
        try {
            boolean success = explanationPointService.save(point);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建讲解点失败", e);
            return Result.error("创建讲解点失败");
        }
    }

    /**
     * 更新讲解点
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新讲解点", notes = "更新讲解点信息")
    public Result<String> updateExplanationPoint(
            @ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid ExplanationPoint point) {
        try {
            ExplanationPoint existPoint = explanationPointService.getById(id);
            if (existPoint == null) {
                return Result.error(404, "讲解点不存在");
            }

            point.setPointId(id);
            boolean success = explanationPointService.updateById(point);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新讲解点失败，id: {}", id, e);
            return Result.error("更新讲解点失败");
        }
    }

    /**
     * 删除讲解点
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除讲解点", notes = "删除讲解点")
    public Result<String> deleteExplanationPoint(@ApiParam(value = "讲解点ID", required = true) @PathVariable Integer id) {
        try {
            ExplanationPoint point = explanationPointService.getById(id);
            if (point == null) {
                return Result.error(404, "讲解点不存在");
            }

            boolean success = explanationPointService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除讲解点失败，id: {}", id, e);
            return Result.error("删除讲解点失败");
        }
    }
}
