<template>
  <div class="explanation-area-detail">
    <div class="page-header">
      <h2>讲解区域详情</h2>
      <div class="header-actions">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="区域ID">
          {{ detail.areaId }}
        </el-descriptions-item>
        <el-descriptions-item label="区域名称">
          {{ detail.areaName }}
        </el-descriptions-item>
        <el-descriptions-item label="关联产品ID">
          {{ detail.productId }}
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sortOrder }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(detail.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(detail.updatedAt) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getExplanationAreaById } from '@/api/explanationArea'
import { formatDateTime } from '@/utils/date'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getExplanationAreaById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取讲解区域详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 操作
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/explanation-area/edit/${route.params.id}`)
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.explanation-area-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
