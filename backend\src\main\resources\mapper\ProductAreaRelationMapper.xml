<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tourism.miniprogram.mapper.ProductAreaRelationMapper">

    <!-- 根据产品ID获取区域关系列表 -->
    <select id="selectByProductId" resultType="com.tourism.miniprogram.entity.ProductAreaRelation">
        SELECT relation_id, product_id, area_id, sort_order, created_at
        FROM product_area_relation
        WHERE product_id = #{productId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 根据区域ID获取产品关系列表 -->
    <select id="selectByAreaId" resultType="com.tourism.miniprogram.entity.ProductAreaRelation">
        SELECT relation_id, product_id, area_id, sort_order, created_at
        FROM product_area_relation
        WHERE area_id = #{areaId}
        ORDER BY sort_order ASC, relation_id DESC
    </select>

    <!-- 批量插入关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO product_area_relation (product_id, area_id, sort_order)
        VALUES
        <foreach collection="relations" item="item" separator=",">
            (#{item.productId}, #{item.areaId}, #{item.sortOrder})
        </foreach>
    </insert>

    <!-- 根据产品ID删除所有关系 -->
    <delete id="deleteByProductId">
        DELETE FROM product_area_relation WHERE product_id = #{productId}
    </delete>

    <!-- 根据区域ID删除所有关系 -->
    <delete id="deleteByAreaId">
        DELETE FROM product_area_relation WHERE area_id = #{areaId}
    </delete>

    <!-- 更新排序 -->
    <update id="updateSortOrder">
        UPDATE product_area_relation 
        SET sort_order = #{sortOrder}
        WHERE relation_id = #{relationId}
    </update>

</mapper>
