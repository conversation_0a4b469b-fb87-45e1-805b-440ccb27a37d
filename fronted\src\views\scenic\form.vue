<template>
  <div class="scenic-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑景区' : '新增景区' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 800px"
      >
        <el-form-item label="景区ID" prop="scenicId">
          <el-input
            v-model="form.scenicId"
            placeholder="系统自动生成"
            :readonly="true"
            style="background-color: #f5f7fa;"
          />
          <div class="form-tip">景区唯一标识，系统自动生成</div>
        </el-form-item>

        <el-form-item label="景区标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入景区标题"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="副标题" prop="subtitle">
          <el-input
            v-model="form.subtitle"
            placeholder="请输入副标题"
            maxlength="25"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="景区描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入景区描述"
            maxlength="25"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="所属省份" prop="provinceId">
              <el-select
                v-model="form.provinceId"
                placeholder="请选择省份"
                style="width: 100%"
                @change="handleProvinceChange"
              >
                <el-option
                  v-for="province in provinceList"
                  :key="province.id"
                  :label="province.name"
                  :value="province.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属城市" prop="cityId">
              <el-select
                v-model="form.cityId"
                placeholder="请选择城市"
                style="width: 100%"
              >
                <el-option
                  v-for="city in cityList"
                  :key="city.id"
                  :label="city.name"
                  :value="city.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="封面图片" prop="image">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.image" :src="form.image" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="相册图片">
          <div class="custom-images-uploader">
            <!-- 第一张图片：顶部宣传图 -->
            <div class="image-slot">
              <div class="image-label">顶部宣传图</div>
              <div
                class="image-upload-area"
                @click="handleImageClick(0)"
              >
                <img
                  v-if="imagesList[0]?.url"
                  :src="imagesList[0].url"
                  class="uploaded-image"
                />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div>点击上传</div>
                </div>
                <div v-if="imagesList[0]?.url" class="image-overlay">
                  <el-icon class="replace-icon"><Edit /></el-icon>
                  <span>点击更换</span>
                </div>
              </div>
            </div>

            <!-- 第二张图片：介绍长图 -->
            <div class="image-slot">
              <div class="image-label">介绍长图</div>
              <div
                class="image-upload-area"
                @click="handleImageClick(1)"
              >
                <img
                  v-if="imagesList[1]?.url"
                  :src="imagesList[1].url"
                  class="uploaded-image"
                />
                <div v-else class="upload-placeholder">
                  <el-icon><Plus /></el-icon>
                  <div>点击上传</div>
                </div>
                <div v-if="imagesList[1]?.url" class="image-overlay">
                  <el-icon class="replace-icon"><Edit /></el-icon>
                  <span>点击更换</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 隐藏的文件输入框 -->
          <input
            ref="fileInputRef"
            type="file"
            accept="image/jpeg,image/png,image/gif,image/webp"
            style="display: none"
            @change="handleFileChange"
          />

          <div class="upload-tip">
            <div>需要上传2张图片，支持jpg/png/gif/webp格式，单张不超过2MB</div>
            <div style="color: #409eff; margin-top: 3px;">
              点击图片可以更换，支持拖拽排序
            </div>
          </div>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="开放时间" prop="openTime">
          <el-input
            v-model="form.openTime"
            placeholder="请输入开放时间"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="地址" prop="address">
          <el-input
            v-model="form.address"
            placeholder="请输入地址"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading, Edit } from '@element-plus/icons-vue'
import { getScenicInfo, createScenic, updateScenic } from '@/api/scenic'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const imagesUploadLoading = ref(false)
const formRef = ref()
const fileInputRef = ref()
const provinceList = ref([])
const cityList = ref([])
const imagesList = ref([])
const currentImageIndex = ref(-1) // 当前要替换的图片索引

const form = reactive({
  scenicId: '',
  title: '',
  subtitle: '',
  description: '',
  image: '',
  images: '',
  openTime: '',
  address: '',
  provinceId: '',
  cityId: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  scenicId: [
    { required: true, message: '景区ID不能为空', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入景区标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  provinceId: [
    { required: true, message: '请选择所属省份', trigger: 'change' }
  ],
  cityId: [
    { required: true, message: '请选择所属城市', trigger: 'change' }
  ]
}

// 生成随机景区ID
const generateScenicId = () => {
  const timestamp = Date.now().toString()
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `scenic_${timestamp}_${randomStr}`
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = data || []
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取城市列表
const fetchCityList = async (provinceId) => {
  if (!provinceId) {
    cityList.value = []
    return
  }
  
  try {
    const { data } = await getCityList(provinceId)
    cityList.value = data || []
  } catch (error) {
    console.error('获取城市列表失败:', error)
  }
}

// 省份变化处理
const handleProvinceChange = (provinceId) => {
  form.cityId = ''
  fetchCityList(provinceId)
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
  const isValidType = allowedTypes.includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('上传图片只能是 JPG/PNG/GIF/WEBP 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 封面图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    // 显示上传进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '封面图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用上传API
    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`封面图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      // 使用预览URL作为图片地址
      form.image = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl
      ElMessage.success('封面图片上传成功')

      // 打印上传结果信息（用于调试）
      console.log('封面图片上传成功:', {
        fileId: response.data.fileId,
        originalFileName: response.data.originalFileName,
        fileSize: response.data.fileSize,
        previewUrl: response.data.previewUrl,
        downloadUrl: response.data.downloadUrl,
        cdnUrl: response.data.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('封面图片上传失败:', error)
    ElMessage.error(error.message || '封面图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 点击图片位置处理
const handleImageClick = (index) => {
  currentImageIndex.value = index
  fileInputRef.value?.click()
}

// 文件选择处理
const handleFileChange = async (event) => {
  const file = event.target.files[0]
  if (!file) return

  // 验证文件
  if (!beforeImageUpload(file)) {
    return
  }

  await uploadImageToIndex(file, currentImageIndex.value)

  // 清空文件输入框
  event.target.value = ''
}

// 上传图片到指定位置
const uploadImageToIndex = async (file, index) => {
  try {
    imagesUploadLoading.value = true

    // 显示上传进度
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '相册图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 调用上传API
    const response = await uploadImage(file, (progress) => {
      loadingInstance.setText(`相册图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    if (response.code === 200 && response.data) {
      // 使用预览URL作为图片地址
      const imageUrl = response.data.previewUrl || response.data.cdnUrl || response.data.downloadUrl

      const imageType = index === 0 ? '顶部宣传图' : '介绍长图'

      // 确保数组有足够的长度
      while (imagesList.value.length <= index) {
        imagesList.value.push(null)
      }

      // 设置或替换指定位置的图片
      imagesList.value[index] = {
        name: response.data.originalFileName || file.name,
        url: imageUrl,
        uid: Date.now() + Math.random()
      }

      updateImagesField()
      ElMessage.success(`${imageType}上传成功`)

      // 打印上传结果信息（用于调试）
      console.log('相册图片上传成功:', {
        type: imageType,
        index: index,
        fileId: response.data.fileId,
        originalFileName: response.data.originalFileName,
        fileSize: response.data.fileSize,
        previewUrl: response.data.previewUrl,
        downloadUrl: response.data.downloadUrl,
        cdnUrl: response.data.cdnUrl
      })
    } else {
      throw new Error(response.message || '上传失败')
    }
  } catch (error) {
    console.error('相册图片上传失败:', error)
    ElMessage.error(error.message || '相册图片上传失败，请重试')
  } finally {
    imagesUploadLoading.value = false
  }
}

// 更新images字段
const updateImagesField = () => {
  const urls = imagesList.value
    .filter(item => item && item.url) // 过滤掉 null 和无效项
    .map(item => item.url)
  form.images = JSON.stringify(urls)
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getScenicInfo(route.params.id)
    Object.assign(form, data)
    
    // 处理相册图片
    if (data.images) {
      try {
        const urls = JSON.parse(data.images)
        imagesList.value = urls.map((url, index) => ({
          name: `image_${index}`,
          url
        }))
      } catch (e) {
        console.error('解析相册图片失败:', e)
      }
    }
    
    // 获取对应的城市列表
    if (data.provinceId) {
      await fetchCityList(data.provinceId)
    }
  } catch (error) {
    ElMessage.error('获取景区详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateScenic(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createScenic(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  imagesList.value = []
  if (isEdit.value) {
    fetchDetail()
  } else {
    // 新增模式下重新生成景区ID
    form.scenicId = generateScenicId()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/scenic/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  if (isEdit.value) {
    fetchDetail()
  } else {
    // 新增模式下生成景区ID
    form.scenicId = generateScenicId()
  }
})
</script>

<style scoped>
.scenic-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 178px;
  height: 178px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.image-uploader-loading {
  width: 178px;
  height: 178px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
  padding: 20px;
}

.upload-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.images-uploader {
  margin-bottom: 10px;
}

.form-tip {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
  line-height: 1.4;
}

/* 自定义相册图片上传样式 */
.custom-images-uploader {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.image-slot {
  flex: 1;
  max-width: 200px;
}

.image-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  text-align: center;
  font-weight: 500;
}

.image-upload-area {
  width: 178px;
  height: 178px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-upload-area:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  font-size: 14px;
}

.upload-placeholder .el-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 14px;
}

.image-upload-area:hover .image-overlay {
  opacity: 1;
}

.replace-icon {
  font-size: 20px;
  margin-bottom: 4px;
}
</style>
