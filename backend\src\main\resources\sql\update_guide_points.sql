-- 更新讲解点表结构
-- 添加讲解点描述图片字段

USE `tourism_miniprogram`;

-- 确保 explanation_point 表存在
CREATE TABLE IF NOT EXISTS `explanation_point` (
  `point_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '讲解点ID',
  `point_name` varchar(255) NOT NULL COMMENT '讲解点名称',
  `point_image` varchar(500) NOT NULL COMMENT '讲解点图片URL',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`point_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='讲解点表';

-- 注释：explanation_point 表已经包含了所需的字段
-- 如果需要添加额外字段，可以使用以下语句：
-- ALTER TABLE explanation_point ADD COLUMN IF NOT EXISTS new_field VARCHAR(255) DEFAULT NULL COMMENT '新字段';
