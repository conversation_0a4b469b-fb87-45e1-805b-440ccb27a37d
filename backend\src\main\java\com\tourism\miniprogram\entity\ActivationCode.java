package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 激活码实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("activation_codes")
@ApiModel(value = "ActivationCode对象", description = "激活码信息")
public class ActivationCode implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "激活码ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "激活码")
    @TableField("code")
    @NotBlank(message = "激活码不能为空")
    private String code;

    @ApiModelProperty(value = "关联产品ID")
    @TableField("product_id")
    private Integer productId;

    @ApiModelProperty(value = "关联组合包ID")
    @TableField("bundle_id")
    private Integer bundleId;

    @ApiModelProperty(value = "关联景区ID")
    @TableField("scenic_id")
    @NotNull(message = "景区ID不能为空")
    private Integer scenicId;

    @ApiModelProperty(value = "绑定的卡券ID")
    @TableField("coupon_id")
    private Integer couponId;

    @ApiModelProperty(value = "状态：unused-未使用，used-已使用，invalid-无效")
    @TableField("status")
    @NotBlank(message = "状态不能为空")
    private String status;

    @ApiModelProperty(value = "使用时间")
    @TableField("used_at")
    private LocalDateTime usedAt;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
