-- 更新讲解产品表结构
-- 如果表名为 product_table，这里不需要重命名操作，因为 product_table 是正确的表名
-- RENAME TABLE guide_products TO product_table;

-- 修改 duration 字段类型从 TIME 改为 VARCHAR
ALTER TABLE product_table MODIFY COLUMN duration VARCHAR(255) COMMENT '讲解总时长';

-- 添加 point_count 字段（如果不存在）
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS point_count INT DEFAULT 0 COMMENT '讲解点数量';

-- 确保讲师表存在
CREATE TABLE IF NOT EXISTS lecturer (
  lecturer_id INT NOT NULL AUTO_INCREMENT COMMENT '讲师ID',
  name VARCHAR(50) NOT NULL COMMENT '讲师姓名',
  avatar_url VARCHAR(200) NOT NULL COMMENT '讲师头像URL',
  title VARCHAR(100) NOT NULL COMMENT '头衔',
  intro TEXT NOT NULL COMMENT '讲师简介',
  expertise VARCHAR(200) DEFAULT NULL COMMENT '专长领域',
  status TINYINT(4) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  sort INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (lecturer_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='讲师表';

-- 插入一些示例讲师数据
INSERT INTO lecturer (name, avatar_url, title, intro, expertise, status, sort) VALUES
('张教授', 'https://example.com/avatar1.jpg', '高级讲师', '资深旅游文化专家，从事旅游讲解工作20年', '历史文化,自然景观', 1, 1),
('李老师', 'https://example.com/avatar2.jpg', '金牌讲师', '专业导游，擅长景区文化讲解', '民俗文化,建筑艺术', 1, 2),
('王导游', 'https://example.com/avatar3.jpg', '资深讲师', '多年景区讲解经验，深受游客喜爱', '地理知识,人文历史', 1, 3)
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 确保 product_table 表结构完整
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS product_id INT NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT '讲解产品唯一标识';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS title VARCHAR(255) NOT NULL COMMENT '讲解产品标题';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS scenic_id INT NOT NULL COMMENT '绑定的景点ID';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS point_count INT DEFAULT 0 COMMENT '讲解点数量';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS duration VARCHAR(255) DEFAULT NULL COMMENT '讲解总时长';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS price DECIMAL(10,2) DEFAULT 0.00 COMMENT '讲解产品价格';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS background_image_url VARCHAR(255) DEFAULT NULL COMMENT '讲解产品的背景图URL';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS example_video_url VARCHAR(255) DEFAULT NULL COMMENT '讲解产品的示例视频URL';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS lecturer_id INT DEFAULT NULL COMMENT '讲师ID';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS map_url VARCHAR(255) DEFAULT NULL COMMENT '景区地图URL';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS start_listening_image_url VARCHAR(255) DEFAULT NULL COMMENT '开始收听介绍图URL';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS description TEXT DEFAULT NULL COMMENT '产品描述';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS status TINYINT(4) DEFAULT 1 COMMENT '状态：1-启用，0-禁用';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS sort INT DEFAULT 0 COMMENT '排序';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';
ALTER TABLE product_table ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间';
