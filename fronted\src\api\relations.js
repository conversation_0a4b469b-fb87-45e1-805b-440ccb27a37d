import request from '@/utils/request'

/**
 * 关系管理API
 */

// ==================== 产品-区域关系管理 ====================

/**
 * 获取产品的区域关系列表
 */
export function getAreasByProductId(productId) {
  return request({
    url: `/relations/product-area/product/${productId}`,
    method: 'get'
  })
}

/**
 * 获取区域的产品关系列表
 */
export function getProductsByAreaId(areaId) {
  return request({
    url: `/relations/product-area/area/${areaId}`,
    method: 'get'
  })
}

/**
 * 添加产品区域关系
 */
export function addProductAreaRelation(data) {
  return request({
    url: '/relations/product-area',
    method: 'post',
    data
  })
}

/**
 * 删除产品区域关系
 */
export function removeProductAreaRelation(productId, areaId) {
  return request({
    url: '/relations/product-area',
    method: 'delete',
    params: { productId, areaId }
  })
}

/**
 * 批量添加产品区域关系
 */
export function batchAddProductAreaRelations(relations) {
  return request({
    url: '/relations/product-area/batch',
    method: 'post',
    data: relations
  })
}

/**
 * 更新产品区域关系排序
 */
export function updateProductAreaSortOrder(relationId, sortOrder) {
  return request({
    url: `/relations/product-area/${relationId}/sort`,
    method: 'put',
    params: { sortOrder }
  })
}

/**
 * 批量更新产品区域关系排序
 */
export function batchUpdateProductAreaSortOrder(relationIds, sortOrders) {
  return request({
    url: '/relations/product-area/batch-sort',
    method: 'put',
    data: { relationIds, sortOrders }
  })
}

// ==================== 区域-讲解点关系管理 ====================

/**
 * 获取区域的讲解点关系列表
 */
export function getPointsByAreaId(areaId) {
  return request({
    url: `/relations/area-point/area/${areaId}`,
    method: 'get'
  })
}

/**
 * 获取讲解点的区域关系列表
 */
export function getAreasByPointId(pointId) {
  return request({
    url: `/relations/area-point/point/${pointId}`,
    method: 'get'
  })
}

/**
 * 添加区域讲解点关系
 */
export function addAreaPointRelation(data) {
  return request({
    url: '/relations/area-point',
    method: 'post',
    data
  })
}

/**
 * 删除区域讲解点关系
 */
export function removeAreaPointRelation(areaId, pointId) {
  return request({
    url: '/relations/area-point',
    method: 'delete',
    params: { areaId, pointId }
  })
}

/**
 * 批量添加区域讲解点关系
 */
export function batchAddAreaPointRelations(relations) {
  return request({
    url: '/relations/area-point/batch',
    method: 'post',
    data: relations
  })
}

/**
 * 更新区域讲解点关系排序
 */
export function updateAreaPointSortOrder(relationId, sortOrder) {
  return request({
    url: `/relations/area-point/${relationId}/sort`,
    method: 'put',
    params: { sortOrder }
  })
}

// ==================== 讲解点-音频关系管理 ====================

/**
 * 获取讲解点的音频关系列表
 */
export function getAudiosByPointId(pointId) {
  return request({
    url: `/relations/point-audio/point/${pointId}`,
    method: 'get'
  })
}

/**
 * 获取音频的讲解点关系列表
 */
export function getPointsByAudioId(audioId) {
  return request({
    url: `/relations/point-audio/audio/${audioId}`,
    method: 'get'
  })
}

/**
 * 添加讲解点音频关系
 */
export function addPointAudioRelation(data) {
  return request({
    url: '/relations/point-audio',
    method: 'post',
    data
  })
}

/**
 * 删除讲解点音频关系
 */
export function removePointAudioRelation(pointId, audioId) {
  return request({
    url: '/relations/point-audio',
    method: 'delete',
    params: { pointId, audioId }
  })
}

/**
 * 批量添加讲解点音频关系
 */
export function batchAddPointAudioRelations(relations) {
  return request({
    url: '/relations/point-audio/batch',
    method: 'post',
    data: relations
  })
}

/**
 * 更新讲解点音频关系排序
 */
export function updatePointAudioSortOrder(relationId, sortOrder) {
  return request({
    url: `/relations/point-audio/${relationId}/sort`,
    method: 'put',
    params: { sortOrder }
  })
}

// ==================== 辅助查询接口 ====================

/**
 * 获取所有区域列表
 */
export function getAllAreas() {
  return request({
    url: '/relations/areas',
    method: 'get'
  })
}

/**
 * 获取所有音频列表
 */
export function getAllAudios() {
  return request({
    url: '/relations/audios',
    method: 'get'
  })
}
