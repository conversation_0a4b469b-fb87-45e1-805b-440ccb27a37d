package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.ProductAreaRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品区域关系Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-09
 */
@Mapper
public interface ProductAreaRelationMapper extends BaseMapper<ProductAreaRelation> {

    /**
     * 根据产品ID获取区域关系列表
     *
     * @param productId 产品ID
     * @return 区域关系列表
     */
    List<ProductAreaRelation> selectByProductId(@Param("productId") Integer productId);

    /**
     * 根据区域ID获取产品关系列表
     *
     * @param areaId 区域ID
     * @return 产品关系列表
     */
    List<ProductAreaRelation> selectByAreaId(@Param("areaId") Integer areaId);

    /**
     * 批量插入关系
     *
     * @param relations 关系列表
     * @return 插入数量
     */
    int batchInsert(@Param("relations") List<ProductAreaRelation> relations);

    /**
     * 根据产品ID删除所有关系
     *
     * @param productId 产品ID
     * @return 删除数量
     */
    int deleteByProductId(@Param("productId") Integer productId);

    /**
     * 根据区域ID删除所有关系
     *
     * @param areaId 区域ID
     * @return 删除数量
     */
    int deleteByAreaId(@Param("areaId") Integer areaId);

    /**
     * 更新排序
     *
     * @param relationId 关系ID
     * @param sortOrder 排序值
     * @return 更新数量
     */
    int updateSortOrder(@Param("relationId") Integer relationId, @Param("sortOrder") Integer sortOrder);
}
