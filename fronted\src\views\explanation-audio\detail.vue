<template>
  <div class="explanation-audio-detail">
    <div class="page-header">
      <h2>讲解音频详情</h2>
      <div class="header-actions">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
      </div>
    </div>

    <el-card v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="音频ID">
          {{ detail.audioId }}
        </el-descriptions-item>
        <el-descriptions-item label="音频名称">
          {{ detail.audioName }}
        </el-descriptions-item>
        <el-descriptions-item label="关联讲解点ID">
          {{ detail.pointId }}
        </el-descriptions-item>
        <el-descriptions-item label="音频时长(秒)">
          {{ detail.duration }}
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ detail.sortOrder }}
        </el-descriptions-item>
        <el-descriptions-item label="音频关联图片" :span="2">
          <el-image
            v-if="detail.audioImage"
            :src="detail.audioImage"
            style="width: 300px; height: 200px"
            fit="cover"
            :preview-src-list="[detail.audioImage]"
          />
          <span v-else>暂无图片</span>
        </el-descriptions-item>
        <el-descriptions-item label="音频文件" :span="2">
          <div v-if="detail.audioUrl" class="audio-player">
            <audio :src="detail.audioUrl" controls style="width: 100%; max-width: 400px;">
              您的浏览器不支持音频播放
            </audio>
          </div>
          <span v-else>暂无音频文件</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDateTime(detail.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDateTime(detail.updatedAt) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getExplanationAudioById } from '@/api/explanationAudio'
import { formatDateTime } from '@/utils/date'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const detail = ref({})

// 获取详情数据
const fetchDetail = async () => {
  try {
    loading.value = true
    const { data } = await getExplanationAudioById(route.params.id)
    detail.value = data
  } catch (error) {
    ElMessage.error('获取讲解音频详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 操作
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  router.push(`/explanation-audio/edit/${route.params.id}`)
}

// 初始化
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.explanation-audio-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.audio-player {
  margin-top: 10px;
}
</style>
