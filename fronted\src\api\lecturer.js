import request from '@/utils/request'

// 获取讲师列表
export function getLecturerList() {
  return request({
    url: '/lecturers',
    method: 'get'
  })
}

// 分页获取讲师列表
export function getLecturerPage(params) {
  return request({
    url: '/lecturers/page',
    method: 'get',
    params
  })
}

// 获取讲师详情
export function getLecturerById(id) {
  return request({
    url: `/lecturers/${id}`,
    method: 'get'
  })
}

// 创建讲师
export function createLecturer(data) {
  return request({
    url: '/lecturers',
    method: 'post',
    data
  })
}

// 更新讲师
export function updateLecturer(id, data) {
  return request({
    url: `/lecturers/${id}`,
    method: 'put',
    data
  })
}

// 删除讲师
export function deleteLecturer(id) {
  return request({
    url: `/lecturers/${id}`,
    method: 'delete'
  })
}
