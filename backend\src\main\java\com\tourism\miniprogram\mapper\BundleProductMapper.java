package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.BundleProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 组合包产品关联Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface BundleProductMapper extends BaseMapper<BundleProduct> {

    /**
     * 根据组合包ID获取产品关联列表
     *
     * @param bundleId 组合包ID
     * @return 产品关联列表
     */
    @Select("SELECT * FROM bundle_products WHERE bundle_id = #{bundleId} ORDER BY id ASC")
    List<BundleProduct> selectByBundleId(Integer bundleId);

    /**
     * 根据产品ID获取组合包关联列表
     *
     * @param productId 产品ID
     * @return 组合包关联列表
     */
    @Select("SELECT * FROM bundle_products WHERE product_id = #{productId} ORDER BY id ASC")
    List<BundleProduct> selectByProductId(Integer productId);
}
