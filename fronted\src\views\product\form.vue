<template>
  <div class="app-container">
    <div class="form-container">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 800px"
      >
        <el-form-item label="产品名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入产品名称"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="景区ID" prop="scenicId">
          <el-input-number
            v-model="form.scenicId"
            :min="1"
            placeholder="请输入景区ID"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="产品类型" prop="productType">
          <el-radio-group v-model="form.productType">
            <el-radio label="single">单品</el-radio>
            <el-radio label="bundle">组合包</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="销售价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入销售价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="有效期(小时)" prop="validityHours">
          <el-input-number
            v-model="form.validityHours"
            :min="0"
            placeholder="请输入有效期小时数"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="需要激活码" prop="requireActivation">
          <el-switch
            v-model="form.requireActivation"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入产品描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getProductById, createProduct, updateProduct } from '@/api/product'

const route = useRoute()
const router = useRouter()

// 响应式数据
const formRef = ref()
const submitLoading = ref(false)

const form = reactive({
  name: '',
  scenicId: null,
  productType: 'single',
  price: null,
  validityHours: 24,
  requireActivation: true,
  description: '',
  status: 1
})

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 1, max: 200, message: '长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  scenicId: [
    { required: true, message: '请输入景区ID', trigger: 'blur' },
    { type: 'number', min: 1, message: '景区ID必须大于0', trigger: 'blur' }
  ],
  productType: [
    { required: true, message: '请选择产品类型', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入销售价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  validityHours: [
    { required: true, message: '请输入有效期小时数', trigger: 'blur' },
    { type: 'number', min: 0, message: '有效期不能小于0', trigger: 'blur' }
  ],
  requireActivation: [
    { required: true, message: '请选择是否需要激活码', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取详情
const fetchDetail = async () => {
  try {
    const { data } = await getProductById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    console.error('获取产品详情失败:', error)
    ElMessage.error('获取产品详情失败')
  }
}

// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    if (isEdit.value) {
      await updateProduct(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createProduct(form)
      ElMessage.success('创建成功')
    }
    
    goBack()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('提交失败:', error)
      ElMessage.error('提交失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 返回
const goBack = () => {
  router.push('/product')
}

// 初始化
onMounted(() => {
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.form-container {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}
</style>
