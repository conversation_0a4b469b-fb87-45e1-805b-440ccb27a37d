import request from '@/utils/request'

// 分页获取评价列表
export function getReviewPage(params) {
  return request({
    url: '/reviews/page',
    method: 'get',
    params
  })
}

// 获取评价详情
export function getReviewById(id) {
  return request({
    url: `/reviews/${id}`,
    method: 'get'
  })
}

// 创建评价
export function createReview(data) {
  return request({
    url: '/reviews',
    method: 'post',
    data
  })
}

// 更新评价
export function updateReview(id, data) {
  return request({
    url: `/reviews/${id}`,
    method: 'put',
    data
  })
}

// 删除评价
export function deleteReview(id) {
  return request({
    url: `/reviews/${id}`,
    method: 'delete'
  })
}
