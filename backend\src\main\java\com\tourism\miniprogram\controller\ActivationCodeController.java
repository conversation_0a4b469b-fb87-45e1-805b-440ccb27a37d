package com.tourism.miniprogram.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.entity.ActivationCode;
import com.tourism.miniprogram.service.ActivationCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * 激活码控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@RestController
@RequestMapping("/activation-codes")
@Api(tags = "激活码管理")
public class ActivationCodeController {

    @Autowired
    private ActivationCodeService activationCodeService;

    /**
     * 分页获取激活码列表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页获取激活码列表", notes = "分页获取激活码列表，支持按编码、状态、景区ID筛选")
    public Result<IPage<ActivationCode>> getActivationCodePage(
            @ApiParam(value = "当前页", defaultValue = "1") @RequestParam(defaultValue = "1") Long current,
            @ApiParam(value = "每页大小", defaultValue = "10") @RequestParam(defaultValue = "10") Long size,
            @ApiParam(value = "激活码") @RequestParam(required = false) String code,
            @ApiParam(value = "状态") @RequestParam(required = false) String status,
            @ApiParam(value = "景区ID") @RequestParam(required = false) Integer scenicId,
            @ApiParam(value = "产品ID") @RequestParam(required = false) Integer productId) {
        try {
            Page<ActivationCode> page = new Page<>(current, size);
            QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(code)) {
                queryWrapper.like("code", code);
            }
            if (StringUtils.hasText(status)) {
                queryWrapper.eq("status", status);
            }
            if (scenicId != null) {
                queryWrapper.eq("scenic_id", scenicId);
            }
            if (productId != null) {
                queryWrapper.eq("product_id", productId);
            }
            queryWrapper.orderByDesc("id");

            IPage<ActivationCode> activationCodePage = activationCodeService.page(page, queryWrapper);
            return Result.success(activationCodePage);
        } catch (Exception e) {
            log.error("分页获取激活码列表失败", e);
            return Result.error("获取激活码列表失败");
        }
    }

    /**
     * 获取激活码列表
     */
    @GetMapping
    @ApiOperation(value = "获取激活码列表", notes = "获取所有激活码列表")
    public Result<List<ActivationCode>> getActivationCodes() {
        try {
            QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();
            queryWrapper.orderByDesc("id");
            List<ActivationCode> codes = activationCodeService.list(queryWrapper);
            return Result.success(codes);
        } catch (Exception e) {
            log.error("获取激活码列表失败", e);
            return Result.error("获取激活码列表失败");
        }
    }

    /**
     * 获取激活码详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取激活码详情", notes = "根据ID获取激活码详情")
    public Result<ActivationCode> getActivationCodeById(@ApiParam(value = "激活码ID", required = true) @PathVariable Integer id) {
        try {
            ActivationCode activationCode = activationCodeService.getById(id);
            if (activationCode == null) {
                return Result.error(404, "激活码不存在");
            }
            return Result.success(activationCode);
        } catch (Exception e) {
            log.error("获取激活码详情失败，id: {}", id, e);
            return Result.error("获取激活码详情失败");
        }
    }

    /**
     * 根据编码获取激活码
     */
    @GetMapping("/code/{code}")
    @ApiOperation(value = "根据编码获取激活码", notes = "根据激活码获取激活码信息")
    public Result<ActivationCode> getActivationCodeByCode(@ApiParam(value = "激活码", required = true) @PathVariable String code) {
        try {
            QueryWrapper<ActivationCode> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code", code);
            ActivationCode activationCode = activationCodeService.getOne(queryWrapper);
            if (activationCode == null) {
                return Result.error(404, "激活码不存在");
            }
            return Result.success(activationCode);
        } catch (Exception e) {
            log.error("根据编码获取激活码失败，code: {}", code, e);
            return Result.error("获取激活码失败");
        }
    }

    /**
     * 批量生成激活码
     */
    @PostMapping("/generate")
    @ApiOperation(value = "批量生成激活码", notes = "批量生成指定数量的激活码")
    public Result<String> generateActivationCodes(
            @ApiParam(value = "景区ID", required = true) @RequestParam Integer scenicId,
            @ApiParam(value = "产品ID") @RequestParam(required = false) Integer productId,
            @ApiParam(value = "组合包ID") @RequestParam(required = false) Integer bundleId,
            @ApiParam(value = "生成数量", defaultValue = "1") @RequestParam(defaultValue = "1") Integer count) {
        try {
            for (int i = 0; i < count; i++) {
                ActivationCode activationCode = new ActivationCode();
                activationCode.setCode(generateUniqueCode());
                activationCode.setScenicId(scenicId);
                activationCode.setProductId(productId);
                activationCode.setBundleId(bundleId);
                activationCode.setStatus("unused");
                activationCodeService.save(activationCode);
            }
            return Result.success("成功生成 " + count + " 个激活码");
        } catch (Exception e) {
            log.error("批量生成激活码失败", e);
            return Result.error("生成激活码失败");
        }
    }

    /**
     * 创建激活码
     */
    @PostMapping
    @ApiOperation(value = "创建激活码", notes = "创建新的激活码")
    public Result<String> createActivationCode(@RequestBody @Valid ActivationCode activationCode) {
        try {
            if (!StringUtils.hasText(activationCode.getCode())) {
                activationCode.setCode(generateUniqueCode());
            }
            boolean success = activationCodeService.save(activationCode);
            if (success) {
                return Result.success("创建成功");
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            log.error("创建激活码失败", e);
            return Result.error("创建激活码失败");
        }
    }

    /**
     * 更新激活码
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新激活码", notes = "根据ID更新激活码信息")
    public Result<String> updateActivationCode(
            @ApiParam(value = "激活码ID", required = true) @PathVariable Integer id,
            @RequestBody @Valid ActivationCode activationCode) {
        try {
            activationCode.setId(id);
            boolean success = activationCodeService.updateById(activationCode);
            if (success) {
                return Result.success("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新激活码失败，id: {}", id, e);
            return Result.error("更新激活码失败");
        }
    }

    /**
     * 删除激活码
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除激活码", notes = "根据ID删除激活码")
    public Result<String> deleteActivationCode(@ApiParam(value = "激活码ID", required = true) @PathVariable Integer id) {
        try {
            boolean success = activationCodeService.removeById(id);
            if (success) {
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除激活码失败，id: {}", id, e);
            return Result.error("删除激活码失败");
        }
    }

    /**
     * 生成唯一激活码
     */
    private String generateUniqueCode() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase();
    }
}
