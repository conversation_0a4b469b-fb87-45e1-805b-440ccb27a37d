<template>
  <div class="explanation-point-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解点' : '新增讲解点' }}</h2>
      <div class="header-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="讲解点名称" prop="pointName">
          <el-input
            v-model="form.pointName"
            placeholder="请输入讲解点名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="关联区域" prop="areaId">
          <el-select
            v-model="form.areaId"
            placeholder="请选择关联区域"
            style="width: 100%"
            filterable
            clearable
            :loading="areaLoading"
          >
            <el-option
              v-for="area in areaList"
              :key="area.areaId"
              :label="`${area.areaName} (ID: ${area.areaId})`"
              :value="area.areaId"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="讲解点图片" prop="pointImage">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.pointImage" :src="form.pointImage" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="排序" prop="sortOrder">
          <el-input-number
            v-model="form.sortOrder"
            :min="0"
            :max="9999"
            placeholder="请输入排序值"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { 
  getExplanationPointById, 
  createExplanationPoint, 
  updateExplanationPoint 
} from '@/api/explanationPoint'
import { getExplanationAreaPage } from '@/api/explanationArea'
import { uploadImage } from '@/api/upload'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const areaLoading = ref(false)
const formRef = ref()
const areaList = ref([])

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const form = reactive({
  pointName: '',
  areaId: null,
  pointImage: '',
  sortOrder: 0
})

// 验证规则
const rules = {
  pointName: [
    { required: true, message: '请输入讲解点名称', trigger: 'blur' },
    { min: 1, max: 100, message: '讲解点名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  areaId: [
    { required: true, message: '请选择关联区域', trigger: 'change' }
  ],
  pointImage: [
    { required: true, message: '请上传讲解点图片', trigger: 'change' }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 0, max: 9999, message: '排序值必须在 0 到 9999 之间', trigger: 'blur' }
  ]
}

// 获取区域列表
const fetchAreaList = async () => {
  try {
    areaLoading.value = true
    const { data } = await getExplanationAreaPage({
      current: 1,
      size: 1000
    })
    areaList.value = data.records || []
  } catch (error) {
    ElMessage.error('获取区域列表失败')
    console.error(error)
  } finally {
    areaLoading.value = false
  }
}

// 获取详情数据（编辑模式）
const fetchDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const { data } = await getExplanationPointById(route.params.id)
    Object.assign(form, data)
  } catch (error) {
    ElMessage.error('获取讲解点详情失败')
    console.error(error)
    router.back()
  } finally {
    loading.value = false
  }
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isImage = /^image\/(jpeg|jpg|png|gif|webp)$/i.test(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传 JPG/PNG/GIF/WEBP 格式的图片!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 处理图片上传
const handleImageUpload = async (options) => {
  try {
    uploadLoading.value = true
    const url = await uploadImage(options.file)
    form.pointImage = url
    ElMessage.success('图片上传成功')
  } catch (error) {
    ElMessage.error('图片上传失败')
    console.error(error)
  } finally {
    uploadLoading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateExplanationPoint(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createExplanationPoint(form)
      ElMessage.success('创建成功')
    }
    
    router.back()
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  router.back()
}

// 初始化
onMounted(() => {
  fetchAreaList()
  fetchDetail()
})
</script>

<style scoped>
.explanation-point-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.2s;
  width: 178px;
  height: 178px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.image {
  width: 178px;
  height: 178px;
  object-fit: cover;
}

.image-uploader-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}
</style>
