/*
 Navicat Premium Data Transfer

 Source Server         : database
 Source Server Type    : MySQL
 Source Server Version : 80042
 Source Host           : localhost:3306
 Source Schema         : tourism_miniprogram

 Target Server Type    : MySQL
 Target Server Version : 80042
 File Encoding         : 65001

 Date: 09/06/2025 00:40:11
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for activation_codes
-- ----------------------------
DROP TABLE IF EXISTS `activation_codes`;
CREATE TABLE `activation_codes`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '激活码',
  `product_id` int NULL DEFAULT NULL COMMENT '关联产品ID',
  `bundle_id` int NULL DEFAULT NULL COMMENT '关联组合包ID',
  `scenic_id` int NOT NULL COMMENT '关联景区ID',
  `coupon_id` int NULL DEFAULT NULL COMMENT '绑定的卡券ID',
  `status` enum('unused','used','invalid') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unused',
  `used_at` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  UNIQUE INDEX `coupon_id`(`coupon_id` ASC) USING BTREE,
  INDEX `product_id`(`product_id` ASC) USING BTREE,
  INDEX `bundle_id`(`bundle_id` ASC) USING BTREE,
  INDEX `idx_code_status`(`code` ASC, `status` ASC) USING BTREE,
  INDEX `idx_scenic_status`(`scenic_id` ASC, `status` ASC) USING BTREE,
  CONSTRAINT `activation_codes_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `activation_codes_ibfk_2` FOREIGN KEY (`bundle_id`) REFERENCES `product_bundles` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `activation_codes_ibfk_3` FOREIGN KEY (`scenic_id`) REFERENCES `scenics` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB Emb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure f_u )pdR
-- ----------------------------
-- Table structure for carousels
-- ----------------------------
DROP TABLE IF EXISTS `carousels`;
CREATE TABLE `carousels`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '图片URL',
  `province_id` int NULL DEFAULT NULL COMMENT '省份ID',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'home' COMMENT '类型：home-首页',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `product_bundles_id` int NULL DEFAULT NULL COMMENT '组合包ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_carousels_province_type`(`province_id` ASC, `type` ASC) USING BTREE,
  INDEX `idx_carousels_status`(`status` ASC) USING BTREE,
  CONSTRAINT `carousels_ibfk_1` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for cities
-- ----------------------------
DROP TABLE IF EXISTS `cities`;
CREATE TABLE `cities`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市名称',
  `province_id` int NOT NULL COMMENT '省份ID',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市代码',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_cities_province_id`(`province_id` ASC) USING BTREE,
  INDEX `idx_cities_status`(`status` ASC) USING BTREE,
  CONSTRAINT `cities_ibfk_1` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for coupons
-- ----------------------------
DROP TABLE IF EXISTS `coupons`;
CREATE TABLE `coupons`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '卡券唯一编码',
  `product_id` int NULL DEFAULT NULL COMMENT '关联产品ID',
  `bundle_id` int NULL DEFAULT NULL COMMENT '关联组合包ID',
  `order_id` int NOT NULL COMMENT '来源订单ID',
  `user_id` int NOT NULL COMMENT '归属用户ID',
  `scenic_id` int NOT NULL COMMENT '关联景区ID',
  `status` enum('unactivated','active','used','expired') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'unactivated',
  `activation_code_id` int NULL DEFAULT NULL COMMENT '绑定的激活码ID',
  `valid_from` datetime NULL DEFAULT NULL COMMENT '生效时间',
  `valid_to` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `used_at` datetime NULL DEFAULT NULL COMMENT '使用时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code` ASC) USING BTREE,
  INDEX `product_id`(`product_id` ASC) USING BTREE,
  INDEX `bundle_id`(`bundle_id` ASC) USING BTREE,
  INDEX `order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_user_scenic_status`(`user_id` ASC, `scenic_id` ASC, `status` ASC) USING BTREE,
  INDEX `idx_validity`(`valid_to` ASC) USING BTREE,
  INDEX `idx_user_status`(`user_id` ASC, `status` ASC) USING BTREE,
  INDEX `idx_scenic_validity`(`scenic_id` ASC, `valid_to` ASC) USING BTREE,
  CONSTRAINT `coupons_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `coupons_ibfk_2` FOREIGN KEY (`bundle_id`) REFERENCES `product_bundles` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `coupons_ibfk_3` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `coupons_ibfk_4` FOREIGN KEY (`scenic_id`) REFERENCES `scenics` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lecturer
-- ----------------------------
DROP TABLE IF EXISTS `lecturer`;
CREATE TABLE `lecturer`  (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '讲师唯一ID',
  `avatar_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '头像URL地址',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '讲师全名',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '职称/头衔',
  `intro` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详细介绍',
  `social_links` json NULL COMMENT '社交媒体链接(JSON格式)',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐讲师',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '讲师表，存储讲师的详细信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_bundles
-- ----------------------------
DROP TABLE IF EXISTS `product_bundles`;
CREATE TABLE `product_bundles`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '组合包名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '组合包描述',
  `discount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '组合包优惠金额',
  `scenic_ids` json NOT NULL COMMENT '包含的景区ID数组',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_table
-- ----------------------------
DROP TABLE IF EXISTS `product_table`;
CREATE TABLE `product_table`  (
  `product_id` int NOT NULL AUTO_INCREMENT COMMENT '讲解产品唯一标识',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解产品标题',
  `scenic_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '绑定的景点ID',
  `point_count` int NOT NULL COMMENT '讲解点数量',
  `duration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解总时长',
  `price` decimal(10, 2) NOT NULL COMMENT '讲解产品价格',
  `background_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解产品的背景图URL',
  `example_video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解产品的示例视频URL',
  `lecturer_id` int NOT NULL COMMENT '讲师ID',
  `map_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '景区地图URL',
  `start_listening_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '开始收听介绍图URL',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  PRIMARY KEY (`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解产品表，存储每个讲解产品的基本信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for explanation_point
-- ----------------------------
DROP TABLE IF EXISTS `explanation_point`;
CREATE TABLE `explanation_point`  (
  `point_id` int NOT NULL AUTO_INCREMENT COMMENT '讲解点唯一标识',
  `point_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解点名称',
  `point_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '讲解点图片URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`point_id`) USING BTREE,
  INDEX `idx_point_status` (`status` ASC) USING BTREE,
  INDEX `idx_point_sort` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解点表，存储每个讲解点的详细信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for products
-- ----------------------------
DROP TABLE IF EXISTS `products`;
CREATE TABLE `products`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `scenic_id` int NOT NULL COMMENT '关联景区ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '产品描述',
  `price` decimal(10, 2) NOT NULL COMMENT '销售价格',
  `validity_hours` int NOT NULL DEFAULT 0 COMMENT '激活后有效期(小时)',
  `example_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '示例图URL',
  `type` enum('single','bundle') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single' COMMENT '产品类型',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_products_scenic` (`scenic_id` ASC) USING BTREE,
  INDEX `idx_products_status` (`status` ASC) USING BTREE,
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`scenic_id`) REFERENCES `scenics` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;
-- Table structure for review_table
-- ----------------------------
DROP TABLE IF EXISTS `review_table`;
CREATE TABLE `review_table`  (
  `review_id` int NOT NULL AUTO_INCREMENT COMMENT '评价唯一标识',
  `product_id` int NOT NULL COMMENT '对应的讲解产品ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '评价内容',
  `review_time` datetime NOT NULL COMMENT '评价时间',
  `user_id` int NOT NULL COMMENT '绑定的用户ID',
  PRIMARY KEY (`review_id`) USING BTREE,
  INDEX `product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `review_table_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `product_table` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价表，存储用户对讲解产品的评价信息' ROW_FORMAT = Dynamic;

-- --------------
-- Table structure for scenics
-- ----------------------------
DROP TABLE IF EXISTS `scenics`;
CREATE TABLE `scenics`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '景区标题',
  `subtitle` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '景区副标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '景区描述',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '主图片',
  `images` json NULL COMMENT '图片集合',
  `open_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开放时间',
  `address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '地址',
  `province_id` int NOT NULL COMMENT '省份ID',
  `city_id` int NOT NULL COMMENT '城市ID',
  `sort` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_scenic_title`(`title` ASC) USING BTREE,
  INDEX `idx_city_scenic`(`city_id` ASC, `status` ASC) USING BTREE,
  CONSTRAINT `scenics_ibfk_1` FOREIGN KEY (`province_id`) REFERENCES `provinces` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `scenics_ibfk_2` FOREIGN KEY (`city_id`) REFERENCES `cities` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for shouye_carousel
-- ----------------------------
DROP TABLE IF EXISTS `shouye_carousel`;
CREATE TABLE `shouye_carousel`  (
  `carousel_id` int NOT NULL AUTO_INCREMENT COMMENT '轮播图唯一标识',
  `image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '轮播图图像的URL',
  PRIMARY KEY (`carousel_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '首页顶部轮播图表，存储轮播图的图像URL和唯一标识' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for usage_records
-- ----------------------------
DROP TABLE IF EXISTS `usage_records`;
CREATE TABLE `usage_records`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `coupon_id` int NOT NULL COMMENT '使用的卡券',
  `user_id` int NOT NULL COMMENT '使用用户',
  `scenic_id` int NOT NULL COMMENT '使用的景区',
  `used_at` datetime NOT NULL COMMENT '使用时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `coupon_id`(`coupon_id` ASC) USING BTREE,
  INDEX `idx_scenic_usage`(`scenic_id` ASC, `used_at` ASC) USING BTREE,
  INDEX `idx_user_usage`(`user_id` ASC, `used_at` ASC) USING BTREE,
  CONSTRAINT `usage_records_ibfk_1` FOREIGN KEY (`coupon_id`) REFERENCES `coupons` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `usage_records_ibfk_2` FOREIGN KEY (`scenic_id`) REFERENCES `scenics` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信unionid',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '城市',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `openid`(`openid` ASC) USING BTREE,
  INDEX `idx_users_openid`(`openid` ASC) USING BTREE,
  INDEX `idx_users_phone`(`phone` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for explanation_area
-- ----------------------------
DROP TABLE IF EXISTS `explanation_area`;
CREATE TABLE `explanation_area`  (
  `area_id` int NOT NULL AUTO_INCREMENT COMMENT '讲解区域ID',
  `area_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '区域名称',
  `area_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '区域描述',
  `area_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域图片URL',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`area_id`) USING BTREE,
  INDEX `idx_area_status` (`status` ASC) USING BTREE,
  INDEX `idx_area_sort` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解区域表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for explanation_audio
-- ----------------------------
DROP TABLE IF EXISTS `explanation_audio`;
CREATE TABLE `explanation_audio`  (
  `audio_id` int NOT NULL AUTO_INCREMENT COMMENT '音频ID',
  `audio_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '音频名称',
  `audio_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '音频描述',
  `audio_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '音频文件URL',
  `audio_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '音频关联图片URL',
  `duration` int NULL DEFAULT 0 COMMENT '音频时长(秒)',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`audio_id`) USING BTREE,
  INDEX `idx_audio_status` (`status` ASC) USING BTREE,
  INDEX `idx_audio_sort` (`sort_order` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解音频表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for product_area_relation
-- ----------------------------
DROP TABLE IF EXISTS `product_area_relation`;
CREATE TABLE `product_area_relation`  (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `product_id` int NOT NULL COMMENT '产品ID',
  `area_id` int NOT NULL COMMENT '区域ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '区域在产品中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_product_area` (`product_id` ASC, `area_id` ASC) USING BTREE,
  INDEX `idx_product_id` (`product_id` ASC) USING BTREE,
  INDEX `idx_area_id` (`area_id` ASC) USING BTREE,
  CONSTRAINT `product_area_relation_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `product_table` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `product_area_relation_ibfk_2` FOREIGN KEY (`area_id`) REFERENCES `explanation_area` (`area_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '产品与区域多对多关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for area_point_relation
-- ----------------------------
DROP TABLE IF EXISTS `area_point_relation`;
CREATE TABLE `area_point_relation`  (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `area_id` int NOT NULL COMMENT '区域ID',
  `point_id` int NOT NULL COMMENT '讲解点ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '讲解点在区域中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_area_point` (`area_id` ASC, `point_id` ASC) USING BTREE,
  INDEX `idx_area_id` (`area_id` ASC) USING BTREE,
  INDEX `idx_point_id` (`point_id` ASC) USING BTREE,
  CONSTRAINT `area_point_relation_ibfk_1` FOREIGN KEY (`area_id`) REFERENCES `explanation_area` (`area_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `area_point_relation_ibfk_2` FOREIGN KEY (`point_id`) REFERENCES `explanation_point` (`point_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '区域与讲解点多对多关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for point_audio_relation
-- ----------------------------
DROP TABLE IF EXISTS `point_audio_relation`;
CREATE TABLE `point_audio_relation`  (
  `relation_id` int NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `point_id` int NOT NULL COMMENT '讲解点ID',
  `audio_id` int NOT NULL COMMENT '音频ID',
  `sort_order` int NULL DEFAULT 0 COMMENT '音频在点中的顺序',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`) USING BTREE,
  UNIQUE INDEX `uniq_point_audio` (`point_id` ASC, `audio_id` ASC) USING BTREE,
  INDEX `idx_point_id` (`point_id` ASC) USING BTREE,
  INDEX `idx_audio_id` (`audio_id` ASC) USING BTREE,
  CONSTRAINT `point_audio_relation_ibfk_1` FOREIGN KEY (`point_id`) REFERENCES `explanation_point` (`point_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `point_audio_relation_ibfk_2` FOREIGN KEY (`audio_id`) REFERENCES `explanation_audio` (`audio_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '讲解点与音频多对多关联表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
