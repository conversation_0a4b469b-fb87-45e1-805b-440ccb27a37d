package com.tourism.miniprogram.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tourism.miniprogram.entity.UsageRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 使用记录Mapper接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Mapper
public interface UsageRecordMapper extends BaseMapper<UsageRecord> {

    /**
     * 根据用户ID获取使用记录
     *
     * @param userId 用户ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM usage_records WHERE user_id = #{userId} ORDER BY used_at DESC")
    List<UsageRecord> selectUsageRecordsByUserId(Integer userId);

    /**
     * 根据景区ID获取使用记录
     *
     * @param scenicId 景区ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM usage_records WHERE scenic_id = #{scenicId} ORDER BY used_at DESC")
    List<UsageRecord> selectUsageRecordsByScenicId(Integer scenicId);

    /**
     * 根据门票ID获取使用记录
     *
     * @param couponId 门票ID
     * @return 使用记录列表
     */
    @Select("SELECT * FROM usage_records WHERE coupon_id = #{couponId} ORDER BY used_at DESC")
    List<UsageRecord> selectUsageRecordsByCouponId(Integer couponId);
}
