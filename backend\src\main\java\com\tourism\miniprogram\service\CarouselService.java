package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Carousel;

import java.util.List;

/**
 * 轮播图服务接口
 *
 * <AUTHOR> Team
 * @since 2023-12-01
 */
public interface CarouselService extends IService<Carousel> {

    /**
     * 根据条件获取轮播图列表
     *
     * @param provinceId 省份ID
     * @param type       类型
     * @return 轮播图列表
     */
    List<Carousel> getCarouselsByCondition(Integer provinceId, String type);
}
